import AppLayout from '@/components/ui/app_layout';
import AddGeneralInfoForm from './components/general_info_section/add_general_info_form';

export default async function Page(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const isAdd = !params.id || params.id === 'add';

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'Master-Setup', link: '/dashboard/setup/master-setup' },
        { title: 'Schedules', link: '/dashboard/setup/master-setup/schedules' },
        {
          title: isAdd ? 'Add Schedule' : 'Edit Schedule',
          link: `/dashboard/setup/master-setup/schedules/${params.id}`,
        },
      ]}
    >
      <AddGeneralInfoForm id={isAdd ? undefined : Number(params.id)} />
    </AppLayout>
  );
}
