import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import InventorySearch from './components/inventory_search';
import WarehouseQuery from '@/services/queries/WarehouseQuery';

export const metadata: Metadata = {
  title: 'Goodkey | Product Price',
};

export default async function ShowFacilityPage() {
  const queryClient = getQueryClient();

  // await queryClient.prefetchQuery({
  //   queryKey: GroupTypeQuery.tags,
  //   queryFn: () => GroupTypeQuery.getAllByGroupIdHierarchical(1),
  // });

  await queryClient.prefetchQuery({
    queryKey: [WarehouseQuery.tags, { warehouseType: 2 }],
    queryFn: () => WarehouseQuery.getAll(2),
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        {
          title: 'Products & Services',
          link: '/dashboard/setup',
        },
        {
          title: 'Inventory & Price Management',
          link: '/dashboard/setup',
        },
        {
          title: 'Product Price Management',
          link: '/dashboard/setup/products-services/inventory/product-price',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        {/* <ProductTable /> */}
        <InventorySearch />
      </HydrationBoundary>
    </AppLayout>
  );
}
