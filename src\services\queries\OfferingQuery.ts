import {
  OfferingDto,
  OfferingDetailDto,
  OfferingPropertyDto,
  OfferingPropertyCreateDto,
  AddonSelectionDto,
} from '@/models/Offering';
import fetcher from './fetcher';
import { urlToFile } from '@/utils/file-helper';
import {
  OfferingCreateData,
  OfferingPropertyCreateData,
  ServiceCreateData,
} from '@/schema/OfferingSchema';
import { OfferingPropertyFormData } from '@/schema/PropertyOptionSchema';

// Helper: Normalize and filter offering property data
const normalizeOfferingPropertyData = (data: OfferingPropertyFormData) => {
  return {
    property: data.property
      .filter((item) => item.options && item.options.length > 0)
      .map((item) => ({
        id: item.id,
        options: item.options!.map(Number), // convert string[] to number[]
      })),
  };
};

const OfferingQuery = {
  tags: ['Offering'] as const,
  getAllByGroupIdFlat: async (groupId: number): Promise<OfferingDto[]> => {
    return fetcher<OfferingDto[]>(`Offering/${groupId}/getAll`);
  },

  getDetail: (id: number): Promise<OfferingDetailDto> => {
    return fetcher<OfferingDetailDto>(`Offering/${id}/Details`);
  },

  // GET: /Offering
  getAll: async (): Promise<OfferingDto[]> => {
    return fetcher<OfferingDto[]>('Offering');
  },

  // GET: /Offering/{id}
  getById: async (id: number): Promise<OfferingDetailDto> => {
    const data = await fetcher<OfferingDetailDto>(`Offering/${id}`);
    const image = data.imagePath
      ? await urlToFile('/images' + data.imagePath)
      : null;

    return {
      ...data,
      image: image ? [image] : [],
    } satisfies OfferingDetailDto;
  },

  // PUT: /Offering/{id}/Description
  updateDescription: async (
    id: number,
    data: { publicDescription: string; internalDescription: string },
  ): Promise<boolean> => {
    return fetcher<boolean>(
      `Offering/${id}/Description`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      },
      true,
    );
  },

  // POST: /Offering
  addService: async (data: ServiceCreateData): Promise<number> => {
    const formData = new FormData();
    if (data.categoryId)
      formData.append('categoryId', data.categoryId.toString());
    if (data.groupTypeId)
      formData.append('groupTypeId', data.groupTypeId.toString());
    if (data.name) formData.append('name', data.name);
    if (data.publicDescription)
      formData.append('publicDescription', data.publicDescription);
    if (data.internalDescription)
      formData.append('internalDescription', data.internalDescription);
    if (data.displayOrder)
      formData.append('displayOrder', data.displayOrder.toString());
    if (data.isForSmOnly !== undefined)
      formData.append('isForSmOnly', String(data.isForSmOnly));
    if (data.isInternalOnly !== undefined)
      formData.append('isInternalOnly', String(data.isInternalOnly));
    if (data.isActive !== undefined)
      formData.append('isActive', String(data.isActive));
    if (data.isObsolete !== undefined)
      formData.append('isObsolete', String(data.isObsolete));

    return fetcher<number>(
      'Offering',
      {
        method: 'POST',
        body: formData,
      },
      true,
    );
  },

  // PUT: /Offering/{id}
  updateService: async (
    id: number,
    data: ServiceCreateData,
  ): Promise<number> => {
    const formData = new FormData();
    if (data.categoryId)
      formData.append('categoryId', data.categoryId.toString());
    if (data.groupTypeId)
      formData.append('groupTypeId', data.groupTypeId.toString());
    if (data.name) formData.append('name', data.name);
    if (data.publicDescription)
      formData.append('publicDescription', data.publicDescription);
    if (data.internalDescription)
      formData.append('internalDescription', data.internalDescription);
    if (data.displayOrder)
      formData.append('displayOrder', data.displayOrder.toString());
    if (data.isForSmOnly !== undefined)
      formData.append('isForSmOnly', String(data.isForSmOnly));
    if (data.isInternalOnly !== undefined)
      formData.append('isInternalOnly', String(data.isInternalOnly));

    if (data.isActive !== undefined)
      formData.append('isActive', String(data.isActive));
    if (data.isObsolete !== undefined)
      formData.append('isObsolete', String(data.isObsolete));

    return fetcher<number>(
      `Offering/${id}`,
      {
        method: 'PUT',
        body: formData,
      },
      true,
    );
  },

  // POST: /Offering
  add: async (data: OfferingCreateData): Promise<number> => {
    const formData = new FormData();
    if (data.categoryId)
      formData.append('categoryId', data.categoryId.toString());
    if (data.groupTypeId)
      formData.append('groupTypeId', data.groupTypeId.toString());
    if (data.name) formData.append('name', data.name);
    if (data.supplierItemNumber)
      formData.append('supplierItemNumber', data.supplierItemNumber);
    if (data.publicDescription)
      formData.append('publicDescription', data.publicDescription);
    if (data.internalDescription)
      formData.append('internalDescription', data.internalDescription);
    if (data.displayOrder)
      formData.append('displayOrder', data.displayOrder.toString());
    if (data.unitChargedId)
      formData.append('unitChargedId', data.unitChargedId.toString());
    if (data.isUnitTypeEach !== undefined)
      formData.append('isUnitTypeEach', String(data.isUnitTypeEach));
    if (data.isAddOn !== undefined)
      formData.append('isAddOn', String(data.isAddOn));
    if (data.isForSmOnly !== undefined)
      formData.append('isForSmOnly', String(data.isForSmOnly));
    if (data.isInternalOnly !== undefined)
      formData.append('isInternalOnly', String(data.isInternalOnly));
    if (data.image?.[0]) formData.append('image', data.image[0]);
    if (data.imagePath) formData.append('imagePath', data.imagePath);
    if (data.isActive !== undefined)
      formData.append('isActive', String(data.isActive));
    if (data.isObsolete !== undefined)
      formData.append('isObsolete', String(data.isObsolete));
    if (data.taxType) {
      data.taxType.forEach((taxId) => {
        formData.append('taxType', taxId);
      });
    }

    return fetcher<number>(
      'Offering',
      {
        method: 'POST',
        body: formData,
      },
      true,
    );
  },

  // PUT: /Offering/{id}
  update: async (id: number, data: OfferingCreateData): Promise<number> => {
    const formData = new FormData();
    if (data.categoryId)
      formData.append('categoryId', data.categoryId.toString());
    if (data.groupTypeId)
      formData.append('groupTypeId', data.groupTypeId.toString());
    if (data.name) formData.append('name', data.name);
    if (data.supplierItemNumber)
      formData.append('supplierItemNumber', data.supplierItemNumber);
    if (data.publicDescription)
      formData.append('publicDescription', data.publicDescription);
    if (data.internalDescription)
      formData.append('internalDescription', data.internalDescription);
    if (data.displayOrder)
      formData.append('displayOrder', data.displayOrder.toString());
    if (data.unitChargedId)
      formData.append('unitChargedId', data.unitChargedId.toString());
    if (data.isUnitTypeEach !== undefined)
      formData.append('isUnitTypeEach', String(data.isUnitTypeEach));
    if (data.isAddOn !== undefined)
      formData.append('isAddOn', String(data.isAddOn));
    if (data.isForSmOnly !== undefined)
      formData.append('isForSmOnly', String(data.isForSmOnly));
    if (data.isInternalOnly !== undefined)
      formData.append('isInternalOnly', String(data.isInternalOnly));
    if (data.image?.[0]) formData.append('image', data.image[0]);
    if (data.imagePath) formData.append('imagePath', data.imagePath);
    if (data.isActive !== undefined)
      formData.append('isActive', String(data.isActive));
    if (data.isObsolete !== undefined)
      formData.append('isObsolete', String(data.isObsolete));
    if (data.taxType) {
      data.taxType.forEach((taxId) => {
        formData.append('taxType', taxId);
      });
    }

    return fetcher<number>(
      `Offering/${id}`,
      {
        method: 'PUT',
        body: formData,
      },
      true,
    );
  },

  // GET: /Offering/{id}/Property
  getOfferingProperty: async (offeringId: number) =>
    fetcher<OfferingPropertyFormData>(`Offering/${offeringId}/Property`),

  getOfferingPropertyDetail: async (offeringId: number) =>
    fetcher<OfferingPropertyDto[]>(`Offering/${offeringId}/PropertyDetails`),

  // POST: /Offering/{id}/Property
  addOfferingPropertyLink:
    (id: number) => async (data: OfferingPropertyFormData) => {
      const normalized = normalizeOfferingPropertyData(data);
      return fetcher<boolean>(`Offering/${id}/Property`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(normalized),
      });
    },

  // PUT: /Offering/{id}/Property
  updateOfferingPropertyLink:
    (id: number) => async (data: OfferingPropertyFormData) => {
      const normalized = normalizeOfferingPropertyData(data);
      return fetcher<boolean>(`Offering/${id}/Property`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(normalized),
      });
    },

  // GET: /Offering/{id}
  getOfferingPropertyById: async (
    id: number,
  ): Promise<OfferingPropertyCreateDto> => {
    const data = await fetcher<OfferingPropertyCreateDto>(
      `Offering/OfferingProperty/${id}`,
    );
    const image = data.imagePath
      ? await urlToFile('/images' + data.imagePath)
      : null;

    return {
      ...data,
      image: image ? [image] : [],
    } satisfies OfferingPropertyCreateDto;
  },

  // PUT: /Offering/OfferingProperty/{id}
  updateOfferingProperty: async (
    id: number,
    data: OfferingPropertyCreateData,
  ): Promise<boolean> => {
    const formData = new FormData();

    if (data.supplierItemNumber)
      formData.append('supplierItemNumber', data.supplierItemNumber);
    if (data.isForSmOnly !== undefined)
      formData.append('isForSmOnly', String(data.isForSmOnly));
    if (data.isInternalOnly !== undefined)
      formData.append('isInternalOnly', String(data.isInternalOnly));
    if (data.image?.[0]) formData.append('image', data.image[0]);
    if (data.imagePath) formData.append('imagePath', data.imagePath);
    if (data.isActive !== undefined)
      formData.append('isActive', String(data.isActive));

    return fetcher<boolean>(
      `Offering/OfferingProperty/${id}`,
      {
        method: 'PUT',
        body: formData,
      },
      true,
    );
  },

  getAddon: async (offeringId: number) =>
    fetcher<AddonSelectionDto>(`Offering/${offeringId}/AddonSelection`),

  // POST: /Offering/{offeringId}/AddonSelection
  saveAddons: async (
    offeringId: number,
    selectedOfferings: number[],
    selectedOptions: number[],
  ): Promise<boolean> => {
    const payload = {
      selectedOfferings,
      selectedOptions,
    };

    return fetcher<boolean>(`Offering/${offeringId}/AddonSelection`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });
  },
};

export default OfferingQuery;
