'use client';

import { useState } from 'react';
import { FileText } from 'lucide-react';
import Image from 'next/image';
import { Button } from '../button';
import {
  FileInput,
  FileUploader,
  FileUploaderContent,
  FileUploaderItem,
  FileDisplayInfo,
} from '../FileInput';
import FileSvgDraw from '../file_svg_draw';

export default function ImprovedFileInputExample() {
  const [files, setFiles] = useState<File[] | null>(null);

  return (
    <div className="p-8 space-y-8 max-w-4xl mx-auto">
      <div>
        <h2 className="text-2xl font-bold mb-2">Improved FileInput Design</h2>
        <p className="text-muted-foreground">
          Demonstrating the improved file input with better overflow handling and responsive design.
        </p>
      </div>

      {/* Single File Upload */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Single File Upload</h3>
        <FileUploader
          value={files}
          onValueChange={setFiles}
          dropzoneOptions={{
            maxFiles: 1,
            maxSize: 10 * 1024 * 1024, // 10MB
            accept: {
              'image/*': ['.jpg', '.jpeg', '.png', '.gif'],
              'application/pdf': ['.pdf'],
              'text/*': ['.txt', '.csv'],
            },
          }}
          className="w-full"
        >
          {files && files.length > 0 ? (
            <FileUploaderContent>
              {files.map((file, i) => (
                <FileUploaderItem key={i} index={i}>
                  <div className="flex items-center gap-3 min-w-0">
                    {/* File Preview */}
                    <div className="flex-shrink-0">
                      {file.type.startsWith('image') ? (
                        <Image
                          src={URL.createObjectURL(file)}
                          alt={file.name}
                          width={48}
                          height={48}
                          className="object-cover rounded-md size-12"
                        />
                      ) : (
                        <div className="size-12 flex items-center justify-center rounded-md bg-muted">
                          <FileText className="size-6 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                    
                    {/* File Info with proper truncation */}
                    <FileDisplayInfo file={file} className="flex-1" />
                  </div>
                </FileUploaderItem>
              ))}
            </FileUploaderContent>
          ) : (
            <FileInput className="border-2 border-dashed border-muted-foreground/25 hover:border-muted-foreground/50 transition-colors">
              <div className="flex items-center justify-center flex-col py-8 px-4">
                <FileSvgDraw />
                <div className="text-center mt-4">
                  <p className="text-sm font-medium">Drop files here or click to browse</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Supports images, PDFs, and text files up to 10MB
                  </p>
                </div>
              </div>
            </FileInput>
          )}
        </FileUploader>
      </div>

      {/* Multiple Files Upload */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Multiple Files Upload</h3>
        <FileUploader
          value={files}
          onValueChange={setFiles}
          dropzoneOptions={{
            maxFiles: 5,
            maxSize: 5 * 1024 * 1024, // 5MB
            multiple: true,
            accept: {
              'image/*': ['.jpg', '.jpeg', '.png', '.gif'],
              'application/pdf': ['.pdf'],
            },
          }}
          className="w-full"
        >
          {files && files.length > 0 ? (
            <FileUploaderContent>
              {files.map((file, i) => (
                <FileUploaderItem key={i} index={i}>
                  <div className="flex items-center gap-3 min-w-0 w-full">
                    {/* File Preview */}
                    <div className="flex-shrink-0">
                      {file.type.startsWith('image') ? (
                        <Image
                          src={URL.createObjectURL(file)}
                          alt={file.name}
                          width={56}
                          height={56}
                          className="object-cover rounded-lg size-14"
                        />
                      ) : (
                        <div className="size-14 flex items-center justify-center rounded-lg bg-muted">
                          <FileText className="size-7 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                    
                    {/* File Info with proper truncation */}
                    <FileDisplayInfo file={file} className="flex-1" />
                  </div>
                </FileUploaderItem>
              ))}
            </FileUploaderContent>
          ) : (
            <FileInput className="border-2 border-dashed border-muted-foreground/25 hover:border-muted-foreground/50 transition-colors">
              <div className="flex items-center justify-center flex-col py-8 px-4">
                <FileSvgDraw />
                <div className="text-center mt-4">
                  <p className="text-sm font-medium">Drop multiple files here or click to browse</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Upload up to 5 files (images or PDFs, max 5MB each)
                  </p>
                </div>
              </div>
            </FileInput>
          )}
        </FileUploader>
      </div>

      {/* Test with Long Filenames */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Test Long Filenames</h3>
        <div className="p-4 bg-muted/50 rounded-lg">
          <p className="text-sm text-muted-foreground mb-2">
            Try uploading files with very long names to test the overflow handling:
          </p>
          <ul className="text-xs text-muted-foreground space-y-1">
            <li>• File names are properly truncated with ellipsis</li>
            <li>• Full filename shown on hover (title attribute)</li>
            <li>• Action buttons remain accessible and properly positioned</li>
            <li>• Responsive design adapts to container width</li>
          </ul>
        </div>
      </div>

      {/* Clear Files Button */}
      {files && files.length > 0 && (
        <div className="flex justify-center">
          <Button 
            variant="outline" 
            onClick={() => setFiles(null)}
            className="w-fit"
          >
            Clear All Files
          </Button>
        </div>
      )}

      {/* Design Improvements Summary */}
      <div className="mt-8 p-6 bg-green-50 border border-green-200 rounded-lg">
        <h4 className="font-semibold text-green-800 mb-3">✅ Design Improvements</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-green-700">
          <div>
            <h5 className="font-medium mb-2">Layout Fixes:</h5>
            <ul className="space-y-1">
              <li>• Fixed overflow issues with long filenames</li>
              <li>• Proper flex layout with min-width constraints</li>
              <li>• Responsive action button positioning</li>
              <li>• Better spacing and padding</li>
            </ul>
          </div>
          <div>
            <h5 className="font-medium mb-2">UX Improvements:</h5>
            <ul className="space-y-1">
              <li>• Hover states and focus indicators</li>
              <li>• Tooltips for action buttons</li>
              <li>• Better file size formatting</li>
              <li>• Improved visual hierarchy</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
