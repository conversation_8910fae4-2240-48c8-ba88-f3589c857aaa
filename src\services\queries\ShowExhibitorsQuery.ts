import fetcher from './fetcher';
import type {
  ShowExhibitorInList,
  ShowExhibitorDetail,
  ShowExhibitorCreateRequest,
  ShowExhibitorUpdateRequest,
  ShowExhibitorArchiveRequest,
  ShowExhibitorStats,
  ShowExhibitorFilters,
} from '@/models/ShowExhibitor';

const ShowExhibitorsQuery = {
  tags: ['ShowExhibitors'] as const,

  // 1. Get Exhibitors by Show
  getByShow: async (showId: number, filters?: ShowExhibitorFilters) => {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, String(value));
        }
      });
    }
    const queryString = params.toString();
    const url = queryString
      ? `ShowExhibitors/show/${showId}?${queryString}`
      : `ShowExhibitors/show/${showId}`;

    return fetcher<ShowExhibitorInList[]>(url);
  },

  // 2. Get Exhibitor by ID
  getById: async (id: number) => {
    return fetcher<ShowExhibitorDetail>(`ShowExhibitors/${id}`);
  },

  // 3. Create Exhibitor
  create: async (data: ShowExhibitorCreateRequest) => {
    return fetcher<boolean>('ShowExhibitors', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
  },

  // 4. Update Exhibitor
  update: (id: number) => async (data: ShowExhibitorUpdateRequest) => {
    return fetcher<boolean>(`ShowExhibitors/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
  },

  // 5. Delete Exhibitor
  delete: async (id: number) => {
    return fetcher<boolean>(`ShowExhibitors/${id}`, {
      method: 'DELETE',
    });
  },

  // 6. Toggle Archive Status
  toggleArchive: (id: number) => async (data: ShowExhibitorArchiveRequest) => {
    return fetcher<boolean>(`ShowExhibitors/${id}/archive`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
  },

  // 7. Get Exhibitor Statistics
  getStats: async (showId: number) => {
    return fetcher<ShowExhibitorStats>(`ShowExhibitors/show/${showId}/stats`);
  },

  // 8. Get Used Booth Numbers
  getUsedBooths: async (showId: number) => {
    return fetcher<string[]>(`ShowExhibitors/show/${showId}/booths`);
  },

  // 9. Check Booth Availability
  checkBoothAvailability: async (
    showId: number,
    boothNumber: string,
    excludeExhibitorId?: number,
  ) => {
    const params = new URLSearchParams();
    params.append('boothNumber', boothNumber);
    if (excludeExhibitorId) {
      params.append('excludeExhibitorId', String(excludeExhibitorId));
    }

    return fetcher<boolean>(
      `ShowExhibitors/show/${showId}/booths/available?${params.toString()}`,
    );
  },
};

export default ShowExhibitorsQuery;
