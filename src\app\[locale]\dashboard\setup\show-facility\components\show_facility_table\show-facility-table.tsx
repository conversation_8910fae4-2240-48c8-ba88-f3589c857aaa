'use client';

import { useQuery } from '@tanstack/react-query';
import { CheckCircle, XCircle } from 'lucide-react';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';
import { Button } from '@/components/ui/button';
import { ShowLocationInList } from '@/models/ShowLocation'; // TS model
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';
import { Link } from '@/utils/navigation';

export const ShowFacilityTable = () => {
  const { data, isLoading } = useQuery({
    queryKey: ShowLocationQuery.tags,
    queryFn: ShowLocationQuery.getAll,
  });

  const { data: provinces, isLoading: isLoadingProvinces } = useQuery({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
  });

  const columns = generateTableColumns<ShowLocationInList>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      locationCode: { name: 'Code', type: 'text', sortable: true },
      city: { name: 'City', type: 'text', sortable: true },
      province: { name: 'Province', type: 'text', sortable: true },
      isArchived: {
        name: 'Active',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <>
                  <XCircle className="text-red-600 w-4 h-4 mr-1" />
                  <span className="text-red-600 hidden">Inactive</span>
                </>
              ) : (
                <>
                  <CheckCircle className="text-green-600 w-4 h-4 mr-1" />
                  <span className="text-green-600 hidden">Active</span>
                </>
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <Link href={`/dashboard/setup/show-facility/${row.id ?? 'add'}`}>
              <Button
                size="sm"
                variant="secondary"
                iconName="EditIcon"
              ></Button>
            </Link>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<ShowLocationInList>({
    name: {
      name: 'Name',
      type: 'text',
    },
    province: {
      name: 'Province',
      type: {
        type: 'select',
        options:
          provinces?.map((province) => ({
            label: province.name,
            value: province.name,
          })) || [],
      },
    },
    city: {
      name: 'City',
      type: 'text',
    },
    isArchived: {
      name: 'Active',
      type: {
        type: 'select',
        options: [
          { label: 'Active', value: 'false' },
          { label: 'Inactive', value: 'true' },
        ],
      },
    },
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      filterFields={filters}
      isLoading={isLoading}
      controls={
        <Link href={`/dashboard/setup/show-facility/add`}>
          <Button variant="primary" iconName="AddIcon">
            Add New Facility
          </Button>
        </Link>
      }
    />
  );
};

export default ShowFacilityTable;
