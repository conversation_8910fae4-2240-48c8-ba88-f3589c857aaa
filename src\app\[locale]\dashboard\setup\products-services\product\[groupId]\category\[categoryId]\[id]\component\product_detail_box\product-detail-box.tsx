'use client';

import './style/index.scss';
import Suspense from '@/components/ui/Suspense';
import { useQuery } from '@tanstack/react-query';
import { formatDateTime } from '@/utils/date-format';
import OfferingQuery from '@/services/queries/OfferingQuery';

interface IProductInfoBox {
  id?: number;
}

function ProductDetailBox({ id }: IProductInfoBox) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Offering Detail', { id }],
    queryFn: () => OfferingQuery.getDetail(id!),
    enabled: !!id,
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {data && (
        <div className="category-info-box">
          <div className="text-sm text-slate-700">
            {data?.code && (
              <div className="flex justify-between">
                <span className="font-medium text-slate-600">Code:</span>
                <span className="whitespace-nowrap">{data.code}</span>
              </div>
            )}
            {data?.categoryName && (
              <div className="flex justify-between">
                <span className="font-medium text-slate-600">
                  Category:&nbsp;
                </span>
                <span className="whitespace-nowrap">{data.categoryName}</span>
              </div>
            )}
            {data?.groupName && (
              <div className="flex justify-between">
                <span className="font-medium text-slate-600">Group:&nbsp;</span>
                <span className="whitespace-nowrap">{data.groupName}</span>
              </div>
            )}
            {/* {data?.createdAt && (
              <div className="flex justify-between">
                <span className="font-medium text-slate-600 whitespace-nowrap">
                  Created At:&nbsp;
                </span>
                <span className="whitespace-nowrap ">
                  {formatDateTime(data.createdAt)}
                </span>
              </div>
            )} */}
            {data?.updatedAt && (
              <div className="flex justify-between">
                <span className="font-medium text-slate-600 whitespace-nowrap">
                  Last Updated:&nbsp;
                </span>
                <span className="whitespace-nowrap">
                  {formatDateTime(data.updatedAt)}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </Suspense>
  );
}

export default ProductDetailBox;
