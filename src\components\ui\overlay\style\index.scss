.overlay-container {
  display: flex;
  position: fixed;
  align-content: stretch;
  width: -webkit-fill-available;
  width: 100%;
  height: -webkit-fill-available;
  height: 100%;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 11;
  padding: 2vh 1vw;

  // Responsive padding adjustments
  @media (max-width: 768px) {
    padding: 1vh 2vw;
  }

  @media (max-width: 480px) {
    padding: 0.5vh 1vw;
  }

  > div {
    display: flex;
    position: absolute;
    align-content: stretch;
    width: -webkit-fill-available;
    width: 100%;
    height: -webkit-fill-available;
    height: 100%;
    justify-content: center;
    align-items: center;
    pointer-events: none;
    > .backdrop {
      display: flex;
      position: fixed;
      width: 100vw;
      height: 100vh;
      flex-direction: column;
      align-content: center;
      justify-content: center;
      align-items: center;
    }
    > .layer {
      display: flex;
      flex-direction: column;
      z-index: 1;

      // Responsive width and height adjustments
      &.responsive-modal {
        // Mobile first approach
        width: 95vw !important;
        max-width: 95vw !important;
        max-height: 90vh;

        // Tablet and up
        @media (min-width: 481px) {
          width: 85vw !important;
          max-width: 85vw !important;
          max-height: 85vh;
        }

        // Desktop small
        @media (min-width: 769px) {
          width: 70vw !important;
          max-width: 70vw !important;
          max-height: 80vh;
        }

        // Desktop medium
        @media (min-width: 1024px) {
          width: 50vw !important;
          max-width: 50vw !important;
          max-height: 80vh;
        }

        // Desktop large
        @media (min-width: 1200px) {
          width: 40vw !important;
          max-width: 40vw !important;
          max-height: 80vh;
        }

        // Ensure content doesn't overflow on small screens
        overflow-y: auto;
        overflow-x: hidden;

        // Better mobile scrolling
        @media (max-width: 480px) {
          overflow-y: auto;
          -webkit-overflow-scrolling: touch;
        }
      }

      > .close-btn {
        align-self: flex-end;
        border-radius: 5px;
        padding-bottom: 5px;
        //fused
        &.inner {
          position: absolute;
          margin: 15px;

          // Responsive close button positioning
          @media (max-width: 480px) {
            margin: 10px;
          }
        }
        &.outer {
          position: static;
          margin-right: -25px;

          // Responsive outer button positioning
          @media (max-width: 480px) {
            margin-right: -15px;
          }
        }
      }
      &:focus {
        outline: none;
      }
      @keyframes appear-top {
        0% {
          transform: translate(0, -50vh);
        }
        100% {
          transform: translate(0, 0);
        }
      }
      @keyframes appear-left {
        0% {
          transform: translate(-50vw, 0);
        }
        100% {
          transform: translate(0, 0);
        }
      }
      @keyframes appear-right {
        0% {
          transform: translate(50vw, 0);
        }
        100% {
          transform: translate(0, 0);
        }
      }
      @keyframes zoom {
        0% {
          transform: scale(0);
        }
        100% {
          transform: translate(1);
        }
      }
      @keyframes appear-bottom {
        0% {
          transform: translate(0, 50vh);
        }
        100% {
          transform: translate(0, 0);
        }
      }
    }
  }
}
