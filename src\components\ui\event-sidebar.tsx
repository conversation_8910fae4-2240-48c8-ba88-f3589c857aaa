'use client';

import {
  CreditCard,
  FileText,
  Package,
  Users,
  BarChart,
  Settings,
  ImageIcon,
  ShoppingCart,
  MessageSquareQuote,
  LucideIcon,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import MenuQuery from '@/services/queries/MenuQuery';

interface EventSidebarProps {
  eventId: string;
  activeItem?: string;
}

// Icon mapping for dynamic menu items
const iconMap: Record<string, LucideIcon> = {
  MessageSquareQuote,
  ShoppingCart,
  FileText,
  Package,
  Users,
  BarChart,
  Settings,
  ImageIcon,
  CreditCard,
};

export function EventSidebar({ eventId, activeItem }: EventSidebarProps) {
  const router = useRouter();

  // Fetch dynamic menu items from API
  const {
    data: menuItems,
    isLoading,
    isError,
  } = useQuery({
    queryKey: [...MenuQuery.tags, 'event_sidebar'],
    queryFn: () => MenuQuery.getBySection('event_sidebar'),
  });

  // Transform API data to component format
  const finalMenuItems =
    menuItems
      ?.map((item) => ({
        name: item.name || 'Menu Item',
        icon: iconMap[item.icon || ''] || Settings, // Default to Settings icon
        url: item.url?.replace('{eventId}', eventId) || '#',
        displayOrder: item.displayOrder,
        isVisible: item.isVisible,
      }))
      .filter((item) => item.isVisible) || [];

  const handleItemClick = (url: string) => {
    router.push(url);
  };

  const isActive = (name: string) => {
    return name === activeItem;
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="w-full md:w-64 bg-white rounded-md border border-slate-200">
        <div className="p-2">
          <div className="animate-pulse space-y-2">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-10 bg-slate-200 rounded-md"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="w-full md:w-64 bg-white rounded-md border border-slate-200">
        <div className="p-4 text-center text-slate-500">
          <p>Unable to load menu</p>
        </div>
      </div>
    );
  }

  // Empty state
  if (!finalMenuItems.length) {
    return (
      <div className="w-full md:w-64 bg-white rounded-md border border-slate-200">
        <div className="p-4 text-center text-slate-500">
          <p>No menu items available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full md:w-64 bg-white rounded-md border border-slate-200">
      <div className="p-2">
        {finalMenuItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <div key={item.name} className="mb-1">
              <button
                className={`w-full flex items-center p-2 rounded-md text-left ${
                  isActive(item.name)
                    ? 'bg-slate-50 text-[#00646C]'
                    : 'text-slate-600 hover:bg-slate-50 hover:text-[#00646C]'
                }`}
                onClick={() => handleItemClick(item.url)}
              >
                <IconComponent className="h-4 w-4 mr-2" />
                <span>{item.name}</span>
              </button>
            </div>
          );
        })}
      </div>
    </div>
  );
}
