'use client';

import { useQuery } from '@tanstack/react-query';
import { DataTable } from '@/components/ui/data-table';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import DepartmentQuery from '@/services/queries/DepartmentQuery';
import { Button } from '@/components/ui/button';
import DepartmentModal from '../department_modal';
import { BriefData } from '@/models/BriefData';

export const DepartmentTable = () => {
  const { data, isLoading } = useQuery({
    queryKey: DepartmentQuery.tags,
    queryFn: DepartmentQuery.getAll,
  });

  const columns = generateTableColumns<BriefData>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      code: { name: 'Code', type: 'text', sortable: true },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <Button
              size="sm"
              variant="secondary"
              iconName="EditIcon"
              onClick={() => {
                modal(<DepartmentModal id={row.id} />, {
                  ...DEFAULT_MODAL,
                  width: '25%',
                }).open();
              }}
            ></Button>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<BriefData>({
    name: {
      name: 'Name',
      type: 'text',
    },
  });

  return (
    <DataTable
      filterFields={filters}
      columns={columns}
      data={data}
      isLoading={isLoading}
      controls={
        <Button
          variant="primary"
          iconName="AddIcon"
          onClick={() => {
            modal(<DepartmentModal />, {
              ...DEFAULT_MODAL,
              width: '25%',
            }).open();
          }}
        >
          Add New Department
        </Button>
      }
    />
  );
};

export default DepartmentTable;
