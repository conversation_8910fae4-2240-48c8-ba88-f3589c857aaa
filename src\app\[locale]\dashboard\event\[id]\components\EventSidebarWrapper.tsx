'use client';

import { usePathname } from 'next/navigation';
import { EventSidebar } from '@/components/ui/event-sidebar';

// Function to determine active sidebar item based on pathname
function getActiveItem(pathname: string, eventId: string): string {
  // Handle exact match for main event page (with or without locale)
  const eventPagePattern = new RegExp(`/dashboard/event/${eventId}$`);
  if (eventPagePattern.test(pathname)) return 'SHOW MANAGEMENT';

  // Handle sub-pages
  if (pathname.includes(`/event/${eventId}/exhibitors`)) return 'EXHIBITORS';
  if (pathname.includes(`/event/${eventId}/orders`)) return 'ORDERS';
  if (pathname.includes(`/event/${eventId}/rfqs`)) return 'RFQs';
  if (pathname.includes(`/event/${eventId}/order-forms`)) return 'ORDER FORMS';
  if (pathname.includes(`/event/${eventId}/show-packages`))
    return 'SHOW PACKAGES';
  if (pathname.includes(`/event/${eventId}/reports`)) return 'LISTS & REPORTS';
  if (pathname.includes(`/event/${eventId}/graphics`)) return 'GRAPHICS';
  if (pathname.includes(`/event/${eventId}/payments`)) return 'PAYMENTS';

  return 'SHOW MANAGEMENT'; // default
}

interface EventSidebarWrapperProps {
  eventId: string;
}

export default function EventSidebarWrapper({
  eventId,
}: EventSidebarWrapperProps) {
  const pathname = usePathname();
  const activeItem = getActiveItem(pathname, eventId);

  return <EventSidebar eventId={eventId} activeItem={activeItem} />;
}
