'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import ShowQuery from '@/services/queries/ShowQuery';
import ShowExhibitorsQuery from '@/services/queries/ShowExhibitorsQuery';
import { ShowExhibitorFilters } from '@/models/ShowExhibitor';
import { ShowGeneralInfoData } from '@/app/[locale]/dashboard/setup/list-of-shows/components/show-tabs/GeneralInfoTab';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';

import { ExhibitorSearchForm } from './components/ExhibitorSearchForm';
import { ExhibitorStats } from './components/ExhibitorStats';
import { ExhibitorsTable } from './components/ExhibitorsTable';

export default function ExhibitorsClient({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const showId = Number(params.id);
  const [searchFilters, setSearchFilters] = useState<ShowExhibitorFilters>({});

  // Fetch show data
  const { data: show, isLoading: isLoadingShow } = useQuery<
    ShowGeneralInfoData,
    Error
  >({
    queryKey: [ShowQuery.tags, showId],
    queryFn: () => ShowQuery.getOne(showId),
  });

  // Fetch location data for show information
  const { data: location } = useQuery({
    queryKey: [ShowLocationQuery.tags, show?.locationId],
    queryFn: () => ShowLocationQuery.getDetail(Number(show!.locationId)),
    enabled: !!show?.locationId,
  });

  // Fetch exhibitors data
  const {
    data: exhibitors,
    isLoading: isLoadingExhibitors,
    refetch: refetchExhibitors,
  } = useQuery({
    queryKey: [ShowExhibitorsQuery.tags, 'show', showId, searchFilters],
    queryFn: () => ShowExhibitorsQuery.getByShow(showId, searchFilters),
    enabled: !!showId,
  });

  // Fetch exhibitor statistics
  const { data: stats, isLoading: isLoadingStats } = useQuery({
    queryKey: [ShowExhibitorsQuery.tags, 'stats', showId],
    queryFn: () => ShowExhibitorsQuery.getStats(showId),
    enabled: !!showId,
  });

  const handleSearch = (filters: ShowExhibitorFilters) => {
    setSearchFilters(filters);
  };

  const handleAddExhibitor = () => {
    router.push(`/dashboard/event/${showId}/exhibitors/add`);
  };

  const handleImportExhibitors = () => {
    router.push(`/dashboard/event/${showId}/exhibitors/import`);
  };

  if (isLoadingShow) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00646C] mx-auto mb-4"></div>
          <p className="text-slate-600">Loading event exhibitors...</p>
        </div>
      </div>
    );
  }

  if (!show) {
    return <div>Show not found</div>;
  }

  return (
    <div className="flex flex-col gap-6">
      {/* Statistics */}
      <ExhibitorStats stats={stats} isLoading={isLoadingStats} />

      {/* Search Form */}
      <ExhibitorSearchForm onSearch={handleSearch} />

      {/* Exhibitors Table */}
      <div className="bg-white rounded-md border border-slate-200 p-6">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-4">
            <h2 className="text-lg font-semibold">Exhibitors</h2>
            {exhibitors && (
              <span className="text-sm text-slate-600">
                There are total {exhibitors.length} exhibitors
              </span>
            )}
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleImportExhibitors}
              iconName="UploadIcon"
            >
              Import from Excel
            </Button>
            <Button
              variant="main"
              onClick={handleAddExhibitor}
              iconName="AddIcon"
            >
              Add New Exhibitor
            </Button>
          </div>
        </div>

        <ExhibitorsTable
          data={exhibitors || []}
          isLoading={isLoadingExhibitors}
          onRefetch={refetchExhibitors}
          showId={showId}
        />
      </div>
    </div>
  );
}
