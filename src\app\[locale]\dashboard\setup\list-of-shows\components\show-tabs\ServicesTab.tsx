'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import ServiceQuery from '@/services/queries/ServiceQuery';
import { Service, ShowService } from '@/models/Service';
import {
  ServiceFormFactory,
  getServiceFormTitle,
} from '@/app/[locale]/dashboard/setup/list-of-shows/components/service-forms/ServiceFormFactory';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChevronDown, ChevronRight } from 'lucide-react';

interface ServicesTabProps {
  showId?: number;
  // onSuccess?: () => void;
}

export default function ServicesTab({ showId }: ServicesTabProps) {
  const queryClient = useQueryClient();
  const [expandedServices, setExpandedServices] = useState<Set<number>>(
    new Set(),
  );

  // Fetch all available services
  const { data: allServices, isLoading: servicesLoading } = useQuery({
    queryKey: ['services'],
    queryFn: ServiceQuery.getAll,
  });

  // Fetch show services (selected services for this show)
  const { data: showServices, isLoading: showServicesLoading } = useQuery({
    queryKey: ['show-services', showId],
    queryFn: () =>
      showId ? ServiceQuery.getShowServices(showId) : Promise.resolve([]),
    enabled: !!showId,
  });

  // Create/Update show service mutation
  const createServiceMutation = useMutation({
    mutationFn: ServiceQuery.createShowService,
    onSuccess: () => {
      toast.success('Service updated successfully');
      queryClient.invalidateQueries({ queryKey: ['show-services', showId] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update service');
    },
  });

  // Update show service mutation
  const updateServiceMutation = useMutation({
    mutationFn: ({ serviceId, data }: { serviceId: number; data: any }) =>
      showId
        ? ServiceQuery.updateShowService(showId, serviceId)(data)
        : Promise.reject('No show ID'),
    onSuccess: () => {
      toast.success('Service configuration saved successfully');
      queryClient.invalidateQueries({ queryKey: ['show-services', showId] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to save service configuration');
    },
  });

  const isLoading = servicesLoading || showServicesLoading;

  if (!showId) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
            Services
          </h2>
          <div className="min-h-[400px] flex items-center justify-center">
            <div className="text-center">
              <p className="text-slate-500">
                Please save the show first to configure services.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const handleServiceToggle = async (service: Service, isSelected: boolean) => {
    if (!showId) return;

    try {
      await createServiceMutation.mutateAsync({
        showId,
        serviceId: service.id,
        isSelected,
      });
    } catch (error) {
      console.error('Failed to toggle service:', error);
    }
  };

  const handleServiceFormSubmit = async (service: Service, formData: any) => {
    if (!showId) return;

    const showService = showServices?.find((ss) => ss.serviceId === service.id);

    if (showService) {
      // Update existing service
      await updateServiceMutation.mutateAsync({
        serviceId: service.id,
        data: {
          isSelected: true,
          formData: {
            serviceId: service.id,
            showId,
            formType: service.serviceFormType,
            data: formData,
          },
        },
      });
    } else {
      // Create new service
      await createServiceMutation.mutateAsync({
        showId,
        serviceId: service.id,
        isSelected: true,
        formData: {
          serviceId: service.id,
          showId,
          formType: service.serviceFormType,
          data: formData,
        },
      });
    }
  };

  const toggleServiceExpansion = (serviceId: number) => {
    const newExpanded = new Set(expandedServices);
    if (newExpanded.has(serviceId)) {
      newExpanded.delete(serviceId);
    } else {
      newExpanded.add(serviceId);
    }
    setExpandedServices(newExpanded);
  };

  const getSelectedService = (serviceId: number): ShowService | undefined => {
    return showServices?.find((ss) => ss.serviceId === serviceId);
  };

  const isServiceSelected = (serviceId: number): boolean => {
    return getSelectedService(serviceId)?.isSelected || false;
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
          Services
        </h2>

        {isLoading ? (
          <div className="min-h-[400px] flex items-center justify-center">
            <div className="text-center">
              <p className="text-slate-500">Loading services...</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {allServices?.map((service) => {
              const isSelected = isServiceSelected(service.id);
              const isExpanded = expandedServices.has(service.id);
              const selectedService = getSelectedService(service.id);

              return (
                <Card key={service.id} className="overflow-hidden">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={(checked) =>
                            handleServiceToggle(service, checked as boolean)
                          }
                          disabled={createServiceMutation.isPending}
                        />
                        <div>
                          <CardTitle className="text-lg">
                            {getServiceFormTitle(service.serviceFormType)}
                          </CardTitle>
                          {service.description && (
                            <p className="text-sm text-slate-600 mt-1">
                              {service.description}
                            </p>
                          )}
                        </div>
                      </div>

                      {isSelected && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleServiceExpansion(service.id)}
                        >
                          {isExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                          <span className="ml-2">Configure</span>
                        </Button>
                      )}
                    </div>
                  </CardHeader>

                  {isSelected && isExpanded && (
                    <CardContent className=" pt-10 bg-slate-50">
                      <ServiceFormFactory
                        serviceType={service.serviceFormType}
                        onSubmit={(formData) =>
                          handleServiceFormSubmit(service, formData)
                        }
                        initialData={selectedService?.formData?.data}
                        isLoading={updateServiceMutation.isPending}
                      />
                    </CardContent>
                  )}
                </Card>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
