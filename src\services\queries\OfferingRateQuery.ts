import {
  GroupTypeWithGroupDto,
  OfferingRateUpsertDto,
} from '@/models/Offering';
import fetcher from './fetcher';

const OfferingRateQuery = {
  tags: ['Offering Rate'] as const,

  getAll: async (groupId: number): Promise<GroupTypeWithGroupDto> => {
    return fetcher<GroupTypeWithGroupDto>(`OfferingRate/group/${groupId}`);
  },

  // GET: /Offering
  getAllByWarehouse: async (
    warehouseId: number,
  ): Promise<GroupTypeWithGroupDto> => {
    return fetcher<GroupTypeWithGroupDto>(
      `OfferingRate/warehouse/${warehouseId}`,
    );
  },

  // POST: /OfferingRate/SaveProperty
  saveOfferingRate: async (
    payload: OfferingRateUpsertDto,
  ): Promise<boolean> => {
    return fetcher<boolean>(`OfferingRate/SaveProperty`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });
  },
};

export default OfferingRateQuery;
