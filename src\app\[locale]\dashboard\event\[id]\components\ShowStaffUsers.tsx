'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import ShowQuery, { ShowStaffUser } from '@/services/queries/ShowQuery';
import AddStaffUserModal from './AddStaffUserModal';
import EditStaffUserModal from './EditStaffUserModal';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';

import {
  UserPlus,
  UserMinus,
  UserCheck,
  UserX,
  Phone,
  Mail,
  Edit,
} from 'lucide-react';
import { modal, DEFAULT_MODAL, FIT_MODAL } from '@/components/ui/overlay';
import { Badge } from '@/components/ui/Badge';
import { toast } from '@/components/ui/use-toast';

interface ShowStaffUsersProps {
  showId: number;
}

export default function ShowStaffUsers({ showId }: ShowStaffUsersProps) {
  const queryClient = useQueryClient();

  const { data: staffUsers, isLoading } = useQuery({
    queryKey: [ShowQuery.tags, showId, 'staff'],
    queryFn: () => ShowQuery.getStaffUsers(showId),
  });

  const handleAddStaffUser = () => {
    modal(<AddStaffUserModal showId={showId} />, {
      ...FIT_MODAL,
    }).open();
  };

  const handleEditStaffUser = (staffUser: ShowStaffUser) => {
    modal(<EditStaffUserModal showId={showId} staffUser={staffUser} />, {
      ...FIT_MODAL,
    }).open();
  };

  const handleRemoveStaffUser = (userId: number, userName: string) => {
    modal(
      ({ close }) => (
        <MutationConfirmModal
          close={close}
          title="Remove Staff User"
          description={`Are you sure you want to remove ${userName} from show staff? This action cannot be undone.`}
          mutateFn={() => ShowQuery.removeStaffUser(showId, userId)}
          mutationKey={[ShowQuery.tags, showId, 'staff']}
          onSuccess={() => {
            queryClient.invalidateQueries({
              queryKey: [ShowQuery.tags, showId, 'staff'],
            });
            toast({
              title: 'Success',
              description: 'Staff user removed successfully',
              variant: 'success',
            });
          }}
          onError={(error: any) => {
            toast({
              title: 'Error',
              description: error.message || 'Failed to remove staff user',
              variant: 'destructive',
            });
          }}
          variant="destructive"
          confirmButtonText="Remove"
        />
      ),
      DEFAULT_MODAL,
    ).open();
  };

  const handleToggleStatus = (
    userId: number,
    userName: string,
    isActive: boolean,
  ) => {
    const action = isActive ? 'deactivate' : 'activate';
    const actionPast = isActive ? 'deactivated' : 'activated';

    modal(
      ({ close }) => (
        <MutationConfirmModal
          close={close}
          title={`${action.charAt(0).toUpperCase() + action.slice(1)} Staff User`}
          description={`Are you sure you want to ${action} ${userName}?`}
          mutateFn={() => ShowQuery.toggleStaffUserStatus(showId, userId)}
          mutationKey={[ShowQuery.tags, showId, 'staff']}
          onSuccess={() => {
            queryClient.invalidateQueries({
              queryKey: [ShowQuery.tags, showId, 'staff'],
            });
            toast({
              title: 'Success',
              description: `Staff user ${actionPast} successfully`,
              variant: 'success',
            });
          }}
          onError={(error: any) => {
            toast({
              title: 'Error',
              description: error.message || `Failed to ${action} staff user`,
              variant: 'destructive',
            });
          }}
          variant={isActive ? 'destructive' : 'default'}
          confirmButtonText={action.charAt(0).toUpperCase() + action.slice(1)}
        />
      ),
      DEFAULT_MODAL,
    ).open();
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            Show Management Additional Users
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-center text-gray-500">Loading staff users...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg">
          Show Management Additional Users
        </CardTitle>
        <Button
          onClick={handleAddStaffUser}
          className="flex items-center gap-2"
          size="sm"
        >
          <UserPlus className="h-4 w-4" />
          Add Staff User
        </Button>
      </CardHeader>
      <CardContent>
        {staffUsers && staffUsers.length > 0 ? (
          <div className="space-y-3">
            {staffUsers.map((staffUser) => (
              <div
                key={staffUser.id}
                className="flex items-center justify-between p-3 border rounded-lg bg-gray-50"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <span className="font-medium text-gray-900">
                      {staffUser.userName}
                    </span>
                    <Badge
                      variant={staffUser.isActive ? 'default' : 'secondary'}
                    >
                      {staffUser.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    {staffUser.userEmail && (
                      <div className="flex items-center gap-1">
                        <Mail className="h-3 w-3" />
                        {staffUser.userEmail}
                      </div>
                    )}
                    {staffUser.userPhone && (
                      <div className="flex items-center gap-1">
                        <Phone className="h-3 w-3" />
                        {staffUser.userPhone}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      handleToggleStatus(
                        staffUser.userId,
                        staffUser.userName,
                        staffUser.isActive,
                      )
                    }
                    className="flex items-center gap-1"
                  >
                    {staffUser.isActive ? (
                      <>
                        <UserX className="h-3 w-3" />
                        Deactivate
                      </>
                    ) : (
                      <>
                        <UserCheck className="h-3 w-3" />
                        Activate
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditStaffUser(staffUser)}
                    className="flex items-center gap-1"
                  >
                    <Edit className="h-3 w-3" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      handleRemoveStaffUser(
                        staffUser.userId,
                        staffUser.userName,
                      )
                    }
                    className="flex items-center gap-1 text-red-600 hover:text-red-700"
                  >
                    <UserMinus className="h-3 w-3" />
                    Remove
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <UserPlus className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">
              No additional staff users assigned to this show.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
