'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import {
  ServiceCreateData,
  ServiceCreateSchema,
} from '@/schema/OfferingSchema';
import OfferingQuery from '@/services/queries/OfferingQuery';
import { DropzoneOptions } from 'react-dropzone';
import { getQueryClient } from '@/utils/query-client';
import { ChevronRight } from 'lucide-react';

interface OfferingGeneralInfoProps {
  id?: number;
  groupId: number;
  categoryId: number;
}

function FormContent({
  defaultValues,
  id,
  groupId,
  categoryId,
}: {
  groupId: number;
  categoryId: number;
  defaultValues?: ServiceCreateData;
  id?: number;
}) {
  const { push } = useRouter();
  const { toast } = useToast();

  const dropzone = {
    accept: {
      'image/*': ['.jpg', '.jpeg', '.png'],
    },
    multiple: false,
    maxFiles: 1,
  } satisfies DropzoneOptions;

  const form = useForm<ServiceCreateData>({
    resolver: zodResolver(ServiceCreateSchema),

    defaultValues: defaultValues ?? {
      categoryId: categoryId.toString(),
      groupTypeId: groupId.toString(),
      name: '',
      code: '',
      publicDescription: '',
      internalDescription: '',
      displayOrder: '',
      isForSmOnly: false,
      isInternalOnly: false,
      isActive: true,
      isObsolete: false,
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: id
      ? (data: ServiceCreateData) => OfferingQuery.updateService(id, data)
      : (data: ServiceCreateData) => OfferingQuery.addService(data),
    onSuccess: async (newId) => {
      if (id) {
        await getQueryClient().invalidateQueries({
          queryKey: OfferingQuery.tags,
        });
        await getQueryClient().invalidateQueries({
          queryKey: ['Offering', { id }],
        });
        await getQueryClient().invalidateQueries({
          queryKey: ['Services', { groupType: 2 }],
        });
      }

      toast({
        title: 'Success',
        description: id
          ? 'Service updated successfully.'
          : 'Service created successfully.',
        variant: 'success',
      });
      const productId = id || newId;
      if (productId) {
        push(
          `/dashboard/setup/products-services/service/${groupId}/category/${categoryId}/${id}/description`,
        );
      } else {
        push(`/dashboard/setup/products-services/service`);
      }
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Something went wrong.',
        variant: 'destructive',
      });
    },
  });

  const isActive = form.watch('isActive');

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => mutate(data))}
        className="space-y-4"
      >
        <h2 className="text-xl font-semibold text-[#00646C] border-b border-slate-200 pb-3">
          Service Information
        </h2>
        <Field
          control={form.control}
          name="name"
          label="Service Name"
          placeholder="Enter service name"
          type="text"
          required
        />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-32 gap-y-2">
          <Field
            control={form.control}
            name="displayOrder"
            label="Display Order"
            placeholder="Enter display order"
            type="text"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-32 gap-y-2">
          <Field
            control={form.control}
            name="isForSmOnly"
            label="Show Manager Only"
            type="checkbox"
          />
          <Field
            control={form.control}
            name="isInternalOnly"
            label="Internal Only"
            type="checkbox"
          />
          <Field
            control={form.control}
            name="isActive"
            label="Active"
            type="checkbox"
          />
          {!isActive && (
            <Field
              control={form.control}
              name="isObsolete"
              label="Obsolete"
              type="checkbox"
            />
          )}
        </div>

        <div className="flex justify-between pt-6 border-t border-slate-200">
          <Button
            type="button"
            variant="outline"
            onClick={() => push('/dashboard/setup/products-services/service')}
          >
            Cancel
          </Button>
          <Button variant="main" type="submit" disabled={isPending}>
            {isPending ? <Spinner className="mr-2 text-white" /> : null}
            Save & Continue
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default function OfferingGeneralInfo({
  id,
  groupId,
  categoryId,
}: OfferingGeneralInfoProps) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Offering', { id }],
    queryFn: () => OfferingQuery.getById(id!),
    enabled: !!id,
    select: (res): ServiceCreateData => ({
      name: res.name ?? '',
      categoryId: res.categoryId?.toString() ?? '',
      groupTypeId: res.groupTypeId?.toString() ?? '',
      displayOrder: res.displayOrder?.toString() ?? '',
      publicDescription: res.publicDescription ?? '',
      internalDescription: res.internalDescription ?? '',
      code: res.code ?? '',
      isActive: res.isActive ?? true,
      isObsolete: res.isObsolete ?? false,
      isForSmOnly: res.isForSmOnly ?? false,
      isInternalOnly: res.isInternalOnly ?? false,
    }),
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <FormContent
        defaultValues={data}
        id={id}
        groupId={groupId}
        categoryId={categoryId}
      />
    </Suspense>
  );
}
