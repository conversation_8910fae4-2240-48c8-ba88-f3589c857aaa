'use client';

import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import ShowExhibitorsQuery from '@/services/queries/ShowExhibitorsQuery';
import { ShowExhibitorInList } from '@/models/ShowExhibitor';
import { modal, DEFAULT_MODAL } from '@/components/ui/overlay';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal/mutation-confirm-modal';

interface ExhibitorsTableProps {
  data: ShowExhibitorInList[];
  isLoading: boolean;
  onRefetch: () => void;
  showId: number;
}

export function ExhibitorsTable({
  data,
  isLoading,
  onRefetch,
  showId,
}: ExhibitorsTableProps) {
  const router = useRouter();
  const queryClient = useQueryClient();

  const handleEdit = (exhibitor: ShowExhibitorInList) => {
    router.push(`/dashboard/event/${showId}/exhibitors/${exhibitor.id}`);
  };

  const handleDelete = (exhibitor: ShowExhibitorInList) => {
    modal(
      ({ close }) => (
        <MutationConfirmModal
          close={close}
          title="Delete Exhibitor"
          description={`Are you sure you want to permanently delete the exhibitor "${exhibitor.companyName}"? This action cannot be undone.`}
          mutateFn={() => ShowExhibitorsQuery.delete(exhibitor.id)}
          mutationKey={[ShowExhibitorsQuery.tags]}
          onSuccess={() => {
            onRefetch();
            queryClient.invalidateQueries({
              queryKey: [ShowExhibitorsQuery.tags, 'stats', showId],
            });
          }}
          variant="destructive"
          confirmButtonText="Delete"
          confirmIconName="DeleteIcon"
        />
      ),
      DEFAULT_MODAL,
    ).open();
  };

  const columns = generateTableColumns<ShowExhibitorInList>(
    {
      boothNumber: {
        name: 'Booth',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-wrap gap-1">
              {row.boothNumber.length > 0 ? (
                row.boothNumber.map((booth) => (
                  <span
                    key={booth}
                    className="inline-block px-2 py-1 text-xs bg-slate-100 text-slate-700 rounded"
                  >
                    {booth}
                  </span>
                ))
              ) : (
                <span className="text-slate-400 text-sm">No booth</span>
              )}
            </div>
          ),
        },
        sortable: false,
      },
      companyName: { name: 'Company', type: 'text', sortable: true },
      contactName: {
        name: 'Contact',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div>
              <div className="font-medium">{row.contactName}</div>
              {row.contactEmail && (
                <div className="text-sm text-slate-500">{row.contactEmail}</div>
              )}
            </div>
          ),
        },
        sortable: true,
      },
      contactTelephone: { name: 'Telephone', type: 'text', sortable: false },
      contactIsRegistered: {
        name: 'Reg',
        type: {
          type: 'node',
          render: ({ row }) => (
            <span
              className={
                row.contactIsRegistered ? 'text-green-600' : 'text-red-600'
              }
            >
              {row.contactIsRegistered ? 'YES' : 'NO'}
            </span>
          ),
        },
        sortable: true,
      },
    },
    {
      actions: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex gap-1">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleEdit(row)}
                iconName="EditIcon"
                title="Edit"
              />

              <Button
                size="sm"
                variant="destructive"
                onClick={() => handleDelete(row)}
                iconName="DeleteIcon"
                title="Delete"
              />
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<ShowExhibitorInList>({
    companyName: { name: 'Company', type: 'text' },
    contactName: { name: 'Contact Name', type: 'text' },
    contactTelephone: { name: 'Telephone', type: 'text' },
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      filterFields={filters}
      isLoading={isLoading}
      disableStripedRows
      preservePagination={false}
    />
  );
}
