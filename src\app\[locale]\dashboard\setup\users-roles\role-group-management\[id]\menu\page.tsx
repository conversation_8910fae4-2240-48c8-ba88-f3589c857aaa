import { redirect } from 'next/navigation';
import { RoleGroupMenuAccessControl } from './component/role_group_menu_access_control';
import { getQueryClient } from '@/utils/query-client';
import RoleGroupQuery from '@/services/queries/RoleGroupQuery';
import MenuQuery from '@/services/queries/MenuQuery';

export default async function RoleGroupMenuPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = await params;

  if (
    resolvedParams.id === 'add' &&
    isNaN(Number.parseInt(resolvedParams.id))
  ) {
    redirect('/dashboard/setup/users-roles/role-management');
  }

  const menuItems = await getQueryClient().fetchQuery({
    queryKey: [...MenuQuery.tags],
    queryFn: MenuQuery.getAll,
  });
  const sections = await getQueryClient().fetchQuery({
    queryKey: [...MenuQuery.tags, 'sections'],
    queryFn: MenuQuery.getSections,
  });
  const selectedItems = await getQueryClient().fetchQuery({
    queryKey: [...RoleGroupQuery.tags, 'menu', { id: resolvedParams.id }],
    queryFn: () => RoleGroupQuery.getMenu(Number.parseInt(resolvedParams.id)),
  });
  const roleGroup = await getQueryClient().fetchQuery({
    queryKey: [...RoleGroupQuery.tags, resolvedParams.id],
    queryFn: () => RoleGroupQuery.getOne(Number.parseInt(resolvedParams.id)),
  });

  return (
    <div>
      <RoleGroupMenuAccessControl
        sections={sections.filter((c) => c.isDashboard).map((c) => c.name)}
        roleGroupId={Number.parseInt(resolvedParams.id)}
        roleGroupName={roleGroup.name}
        menuItems={menuItems.filter((m) => !m.parent && m.isDashboard)}
        selectedItems={selectedItems}
      />
    </div>
  );
}
