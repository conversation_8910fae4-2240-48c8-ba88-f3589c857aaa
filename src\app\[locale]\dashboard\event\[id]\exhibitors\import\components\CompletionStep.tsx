'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/Badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { CheckCircle, AlertCircle, Eye, RefreshCw } from 'lucide-react';
import type { ExhibitorImportExecutionResponseDto } from '@/models/ExhibitorImport';

interface CompletionStepProps {
  executionData: ExhibitorImportExecutionResponseDto;
  onStartOver: () => void;
}

const CompletionStep: React.FC<CompletionStepProps> = ({
  executionData,
  onStartOver,
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [selectedTab, setSelectedTab] = useState('summary');

  const { summary, results } = executionData;

  // Safe access with fallbacks
  const stats = {
    totalRows: summary.totalRows || 0,
    processedRows: summary.processedRows || 0,
    successfulRows: summary.successfulRows || 0,
    failedRows: summary.failedRows || 0,
    companiesCreated: summary.companiesCreated || 0,
    companiesUpdated: summary.companiesUpdated || 0,
    contactsCreated: summary.contactsCreated || 0,
    contactsUpdated: summary.contactsUpdated || 0,
    exhibitorsCreated: summary.exhibitorsCreated || 0,
    usersCreated: summary.usersCreated || 0,
    emailsSent: summary.emailsSent || 0,
  };

  const successfulResults = results.filter((r) => r.status === 'Success');
  const failedResults = results.filter((r) => r.status === 'Failed');

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
        <h3 className="text-lg font-semibold">
          Import Completed Successfully!
        </h3>
        <p className="text-muted-foreground">
          Processed {stats.processedRows} of {stats.totalRows} rows
        </p>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {stats.companiesCreated}
            </div>
            <div className="text-sm text-muted-foreground">
              Companies Created
            </div>
            {stats.companiesUpdated > 0 && (
              <div className="text-xs text-blue-500">
                +{stats.companiesUpdated} updated
              </div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {stats.contactsCreated}
            </div>
            <div className="text-sm text-muted-foreground">
              Contacts Created
            </div>
            {stats.contactsUpdated > 0 && (
              <div className="text-xs text-green-500">
                +{stats.contactsUpdated} updated
              </div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">
              {stats.exhibitorsCreated}
            </div>
            <div className="text-sm text-muted-foreground">
              Exhibitors Created
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">
              {stats.usersCreated}
            </div>
            <div className="text-sm text-muted-foreground">Users Created</div>
            {stats.emailsSent > 0 && (
              <div className="text-xs text-orange-500">
                {stats.emailsSent} emails sent
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Processing Results Summary */}
      <div className="grid grid-cols-2 gap-4">
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-700">
              {stats.successfulRows}
            </div>
            <div className="text-sm text-green-600">Successful Rows</div>
          </CardContent>
        </Card>
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-700">
              {stats.failedRows}
            </div>
            <div className="text-sm text-red-600">Failed Rows</div>
          </CardContent>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4">
        <Button
          variant="outline"
          onClick={() => setShowDetails(!showDetails)}
          className="flex items-center space-x-2"
        >
          <Eye className="h-4 w-4" />
          <span>{showDetails ? 'Hide Details' : 'View Details'}</span>
        </Button>
        <Button onClick={onStartOver} className="flex items-center space-x-2">
          <RefreshCw className="h-4 w-4" />
          <span>Import Another File</span>
        </Button>
      </div>

      {/* Detailed Results */}
      {showDetails && (
        <Card>
          <CardHeader>
            <CardTitle>Detailed Import Results</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={selectedTab} onValueChange={setSelectedTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="successful">
                  Successful ({successfulResults.length})
                </TabsTrigger>
                <TabsTrigger value="failed">
                  Failed ({failedResults.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="summary" className="space-y-4">
                <div className="grid gap-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Import Statistics</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">
                          Total Rows:
                        </span>
                        <span className="ml-2 font-medium">
                          {stats.totalRows}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">
                          Processed:
                        </span>
                        <span className="ml-2 font-medium">
                          {stats.processedRows}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">
                          Success Rate:
                        </span>
                        <span className="ml-2 font-medium">
                          {stats.totalRows > 0
                            ? Math.round(
                                (stats.successfulRows / stats.totalRows) * 100,
                              )
                            : 0}
                          %
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">
                          Completion:
                        </span>
                        <span className="ml-2 font-medium">
                          {executionData.completedAt
                            ? new Date(
                                executionData.completedAt,
                              ).toLocaleString()
                            : 'N/A'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="successful" className="space-y-4">
                {successfulResults.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <AlertCircle className="h-12 w-12 mx-auto mb-4 text-yellow-600" />
                    <p>No successful imports found.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {successfulResults.slice(0, 20).map((result) => (
                      <Card key={result.rowNumber} className="border-green-200">
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start mb-2">
                            <span className="font-medium">
                              Row {result.rowNumber}
                            </span>
                            <Badge className={getStatusColor(result.status)}>
                              {result.status}
                            </Badge>
                          </div>
                          <div className="text-sm space-y-1">
                            <p>
                              <strong>Company:</strong> {result.companyName}
                            </p>
                            <p>
                              <strong>Contact:</strong> {result.contactName}
                            </p>
                            <p>
                              <strong>Email:</strong> {result.contactEmail}
                            </p>
                          </div>
                          <div className="mt-2 text-xs text-muted-foreground">
                            <p>
                              Processed:{' '}
                              {new Date(result.processedAt).toLocaleString()}
                            </p>
                            {result.createdCompanyId && (
                              <p>Company ID: {result.createdCompanyId}</p>
                            )}
                            {result.createdContactId && (
                              <p>Contact ID: {result.createdContactId}</p>
                            )}
                            {result.createdExhibitorId && (
                              <p>Exhibitor ID: {result.createdExhibitorId}</p>
                            )}
                            {result.createdUserId && (
                              <p>User ID: {result.createdUserId}</p>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                    {successfulResults.length > 20 && (
                      <p className="text-center text-muted-foreground">
                        ... and {successfulResults.length - 20} more successful
                        imports
                      </p>
                    )}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="failed" className="space-y-4">
                {failedResults.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-600" />
                    <p>No failed imports! All rows processed successfully.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {failedResults.map((result) => (
                      <Card key={result.rowNumber} className="border-red-200">
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start mb-2">
                            <span className="font-medium">
                              Row {result.rowNumber}
                            </span>
                            <Badge className={getStatusColor(result.status)}>
                              {result.status}
                            </Badge>
                          </div>
                          <div className="text-sm space-y-1">
                            <p>
                              <strong>Company:</strong> {result.companyName}
                            </p>
                            <p>
                              <strong>Contact:</strong> {result.contactName}
                            </p>
                            <p>
                              <strong>Email:</strong> {result.contactEmail}
                            </p>
                          </div>
                          {result.errorMessage && (
                            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                              <strong>Error:</strong> {result.errorMessage}
                            </div>
                          )}
                          <div className="mt-2 text-xs text-muted-foreground">
                            <p>
                              Processed:{' '}
                              {new Date(result.processedAt).toLocaleString()}
                            </p>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CompletionStep;
