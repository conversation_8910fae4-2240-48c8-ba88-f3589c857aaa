import { z } from 'zod';

// Janitorial Services Schema
export const JanitorialServicesSchema = z.object({
  serviceIncludes: z.string().min(1, 'Service description is required'),
  rateType: z.enum(['sq_ft', 'hourly', 'daily']),
  oneTimeRate: z.number().min(0, 'Rate must be non-negative'),
  twoDaysRate: z.number().min(0, 'Rate must be non-negative'),
  threeDaysRate: z.number().min(0, 'Rate must be non-negative'),
});

// Labour (Installation and Dismantle) Schema
export const LabourInstallationDismantleSchema = z.object({
  serviceIncludes: z.string().min(1, 'Service description is required'),
  minimumHour: z.enum(['1_hour', '2_hours']),
  regularTimeRate: z.number().min(0, 'Rate must be non-negative'),
  minimumCharge: z.number().min(0, 'Minimum charge must be non-negative'),
  daysHours: z.string().optional(),
  overtimeRate: z.number().min(0, 'Rate must be non-negative'),
  overtimeMinimumCharge: z
    .number()
    .min(0, 'Minimum charge must be non-negative'),
  overtimeDaysHours: z.string().optional(),
  doubleTimeRate: z.number().min(0, 'Rate must be non-negative'),
  doubleTimeMinimumCharge: z
    .number()
    .min(0, 'Minimum charge must be non-negative'),
  doubleTimeDaysHours: z.string().optional(),
  supervisionRate: z.number().min(0, 'Rate must be non-negative'),
  supervisorNotes: z.string().optional(),
  ladderPrices: z.object({
    sixFeet: z.number().min(0, 'Price must be non-negative'),
    eightFeet: z.number().min(0, 'Price must be non-negative'),
    tenFeet: z.number().min(0, 'Price must be non-negative'),
    twelveFeet: z.number().min(0, 'Price must be non-negative'),
  }),
});

// Labour by Time Range Schema
export const LabourByTimeRangeSchema = z.object({
  shift: z.object({
    regularTime: z.object({
      from: z.string().min(1, 'Start time is required'),
      to: z.string().min(1, 'End time is required'),
      weekdays: z.number().min(0, 'Rate must be non-negative'),
      weekends: z.number().min(0, 'Rate must be non-negative'),
    }),
    overtime: z.object({
      from: z.string().min(1, 'Start time is required'),
      to: z.string().min(1, 'End time is required'),
      weekdays: z.number().min(0, 'Rate must be non-negative'),
      weekends: z.number().min(0, 'Rate must be non-negative'),
    }),
    doubleTime: z.object({
      from: z.string().min(1, 'Start time is required'),
      to: z.string().min(1, 'End time is required'),
      weekdays: z.number().min(0, 'Rate must be non-negative'),
      weekends: z.number().min(0, 'Rate must be non-negative'),
    }),
  }),
  supervisionRate: z.number().min(0, 'Rate must be non-negative'),
  ladderPrice: z.object({
    sixFeet: z.number().min(0, 'Price must be non-negative'),
    eightFeet: z.number().min(0, 'Price must be non-negative'),
    tenFeet: z.number().min(0, 'Price must be non-negative'),
    twelveFeet: z.number().min(0, 'Price must be non-negative'),
  }),
  installation: z.object({
    minimumCharge: z.number().min(0, 'Minimum charge must be non-negative'),
    minimumHour: z.number().min(0, 'Minimum hour must be non-negative'),
  }),
  dismantle: z.object({
    minimumCharge: z.number().min(0, 'Minimum charge must be non-negative'),
    minimumHour: z.number().min(0, 'Minimum hour must be non-negative'),
  }),
});

// On-Site Material Handling Schema
export const OnSiteMaterialHandlingSchema = z.object({
  receivingAddress: z.string().min(1, 'Receiving address is required'),
  daysHours: z.string().min(1, 'Days and hours are required'),
  pricePerWeight: z.number().min(0, 'Price must be non-negative'),
  weight: z.number().min(0, 'Weight must be non-negative'),
  weightUnit: z.enum(['lbs', 'kg']),
  minimumCharge: z.number().min(0, 'Minimum charge must be non-negative'),
});

// Porter Service Schema
export const PorterServiceSchema = z.object({
  minimumHours: z.number().min(1, 'Minimum hours is required'),
  minimumDays: z.number().min(1, 'Minimum days is required'),
  minimumLabourers: z.number().min(1, 'Minimum labourers is required'),
  rateType: z.enum(['sq_ft', 'hourly', 'daily']),
  rates: z
    .array(
      z.object({
        rate: z.number().min(0, 'Rate must be non-negative'),
        exhibitArea: z.number().min(0, 'Exhibit area must be non-negative'),
      }),
    )
    .min(1, 'At least one rate is required'),
});

// Mandatory Storage Service Schema
export const MandatoryStorageServiceSchema = z.object({
  // This is a simple service with no additional fields
});

// Forklift Service Schema
export const ForkliftServiceSchema = z.object({
  rates: z
    .array(
      z.object({
        weight: z.number().min(0, 'Weight must be non-negative'),
        regularRate: z.number().min(0, 'Rate must be non-negative'),
        overtimeRate: z.number().min(0, 'Rate must be non-negative'),
        doubleRate: z.number().min(0, 'Rate must be non-negative'),
      }),
    )
    .min(1, 'At least one rate is required'),
  daysHours: z.object({
    regular: z.string().optional(),
    regularMinimumCharge: z
      .number()
      .min(0, 'Minimum charge must be non-negative'),
    overtime: z.string().optional(),
    overtimeMinimumCharge: z
      .number()
      .min(0, 'Minimum charge must be non-negative'),
    doubleTime: z.string().optional(),
    doubleTimeMinimumCharge: z
      .number()
      .min(0, 'Minimum charge must be non-negative'),
  }),
  additionalServices: z.object({
    manCageRate: z.number().min(0, 'Rate must be non-negative'),
    boomRate: z.number().min(0, 'Rate must be non-negative'),
  }),
});

// Export all schema types
export type JanitorialServicesFormType = z.infer<
  typeof JanitorialServicesSchema
>;
export type LabourInstallationDismantleFormType = z.infer<
  typeof LabourInstallationDismantleSchema
>;
export type LabourByTimeRangeFormType = z.infer<typeof LabourByTimeRangeSchema>;
export type OnSiteMaterialHandlingFormType = z.infer<
  typeof OnSiteMaterialHandlingSchema
>;
export type PorterServiceFormType = z.infer<typeof PorterServiceSchema>;
export type MandatoryStorageServiceFormType = z.infer<
  typeof MandatoryStorageServiceSchema
>;
export type ForkliftServiceFormType = z.infer<typeof ForkliftServiceSchema>;

// Schema mapping for dynamic form rendering
export const ServiceFormSchemas = {
  JANITORIAL_SERVICES: JanitorialServicesSchema,
  LABOUR_INSTALLATION_DISMANTLE: LabourInstallationDismantleSchema,
  LABOUR_BY_TIME_RANGE: LabourByTimeRangeSchema,
  ON_SITE_MATERIAL_HANDLING: OnSiteMaterialHandlingSchema,
  PORTER_SERVICE: PorterServiceSchema,
  MANDATORY_STORAGE_SERVICE: MandatoryStorageServiceSchema,
  FORKLIFT_SERVICE: ForkliftServiceSchema,
} as const;
