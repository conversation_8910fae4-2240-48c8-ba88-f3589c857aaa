import { z } from 'zod';

// Base schema for exhibitor creation
export const ShowExhibitorBaseSchema = z.object({
  showId: z.number(),
  companyId: z.string().optional(),
  contactId: z.string().optional(),
  boothNumber: z.string().optional(), // Changed from array to string for tags input
  useExistingContact: z.boolean().default(false),
  createNewCompany: z.boolean().default(false),
});

// Contact creation fields schema
export const ContactCreationSchema = z.object({
  contactTypeId: z.string().default('1'),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().optional(),
  telephone: z.string().optional(),
  ext: z.string().optional(),
  cellphone: z.string().optional(),
  sendEmailInvite: z.boolean().default(true),
});

// Company creation fields schema
export const CompanyCreationSchema = z.object({
  companyName: z.string().optional(),
  companyPhone: z.string().optional(),
  companyEmail: z.string().optional(),
  companyAddress1: z.string().optional(),
  companyAddress2: z.string().optional(),
  companyCity: z.string().optional(),
  companyProvinceId: z.string().optional(),
  companyPostalCode: z.string().optional(),
  companyCountryId: z.string().optional(),
  companyWebsiteUrl: z.string().optional(),
  // companyAccountNumber: z.string().optional(),
  companyNote: z.string().optional(),
});

// Combined schema with conditional validation
export const ShowExhibitorCreateSchema = ShowExhibitorBaseSchema.and(
  ContactCreationSchema,
)
  .and(CompanyCreationSchema)
  .refine(
    (data) => {
      // If using existing contact, contactId is required
      if (data.useExistingContact) {
        return data.contactId && data.contactId.length > 0;
      }
      // If creating new contact, firstName is required
      return data.firstName && data.firstName.length > 0;
    },
    {
      message: 'Either select an existing contact or provide contact details',
      path: ['contactId'],
    },
  )
  .refine(
    (data) => {
      // If creating new contact and email is provided, it must be valid
      if (!data.useExistingContact && data.email && data.email.length > 0) {
        return z.string().email().safeParse(data.email).success;
      }
      return true;
    },
    {
      message: 'Invalid email address',
      path: ['email'],
    },
  );

// Update schema - supports all the new update scenarios
export const ShowExhibitorUpdateSchema = ShowExhibitorCreateSchema;

// Archive schema
export const ShowExhibitorArchiveSchema = z.object({
  archiveReason: z.string().optional(),
});

// Search filters schema
export const ExhibitorSearchSchema = z.object({
  searchType: z.enum(['booth', 'company', 'all']).default('all'),
  searchTerm: z.string().optional(),
  companyName: z.string().optional(),
  contactName: z.string().optional(),
  boothNumber: z.string().optional(),
  isActive: z.boolean().optional(),
  isArchived: z.boolean().optional(),
});

// Type exports
export type ShowExhibitorCreateFormData = z.infer<
  typeof ShowExhibitorCreateSchema
>;
export type ShowExhibitorUpdateFormData = z.infer<
  typeof ShowExhibitorUpdateSchema
>;
export type ShowExhibitorArchiveFormData = z.infer<
  typeof ShowExhibitorArchiveSchema
>;
export type ExhibitorSearchFormData = z.infer<typeof ExhibitorSearchSchema>;
