'use client';

import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import Suspense from '@/components/ui/Suspense';
import { toast } from '@/components/ui/use-toast';
import ShowExhibitorsQuery from '@/services/queries/ShowExhibitorsQuery';
import CompanyQuery from '@/services/queries/CompanyQuery';

import {
  ShowExhibitorCreateSchema,
  ShowExhibitorCreateFormData,
} from '@/schema/ShowExhibitorSchema';
import Field from '@/components/ui/inputs/field/field';
import ContactForm from './ContactForm';
import CompanyForm from './company-form';

function FormContent({
  defaultValues,
  showId,
  exhibitorId,
  isAdd,
}: {
  defaultValues?: ShowExhibitorCreateFormData;
  showId: number;
  exhibitorId?: number;
  isAdd: boolean;
}) {
  const router = useRouter();
  const queryClient = useQueryClient();

  // State for company editing/creation
  const [showCompanyEdit, setShowCompanyEdit] = useState(false);
  const [editCompany, setEditCompany] = useState(false);
  const [createNewCompany, setCreateNewCompany] = useState(false);

  // Fetch companies
  const { data: companies } = useQuery({
    queryKey: [CompanyQuery.tags, 'exhibitor'],
    queryFn: () => CompanyQuery.getAll('exhibitor'),
  });

  const form = useForm<ShowExhibitorCreateFormData>({
    resolver: zodResolver(ShowExhibitorCreateSchema),
    defaultValues: defaultValues || {
      showId,
      companyId: '',
      useExistingContact: false,
      contactTypeId: '1',
      firstName: '',
      lastName: '',
      email: '',
      telephone: '',
      ext: '',
      cellphone: '',
      sendEmailInvite: true,
      boothNumber: '',
    },
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: ShowExhibitorsQuery.create,
    onSuccess: () => {
      // Invalidate show exhibitors queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: [ShowExhibitorsQuery.tags, showId],
      });

      // Invalidate company queries to refresh company-related data
      queryClient.invalidateQueries({
        queryKey: [...CompanyQuery.tags],
      });

      // Specifically invalidate the exhibitor company list
      queryClient.invalidateQueries({
        queryKey: [...CompanyQuery.tags, 'exhibitor'],
      });

      toast({
        title: 'Success',
        description: 'Exhibitor created successfully',
        variant: 'success',
      });
      router.push(`/dashboard/event/${showId}/exhibitors`);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create exhibitor',
        variant: 'destructive',
      });
    },
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ShowExhibitorsQuery.update(exhibitorId!),
    onSuccess: () => {
      // Invalidate show exhibitors queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: [ShowExhibitorsQuery.tags, showId],
      });
      // Also invalidate the specific exhibitor query
      queryClient.invalidateQueries({
        queryKey: [ShowExhibitorsQuery.tags, exhibitorId],
      });

      // Invalidate company queries to refresh company-related data
      queryClient.invalidateQueries({
        queryKey: [...CompanyQuery.tags],
      });

      // Specifically invalidate the exhibitor company list
      queryClient.invalidateQueries({
        queryKey: [...CompanyQuery.tags, 'exhibitor'],
      });

      toast({
        title: 'Success',
        description: 'Exhibitor updated successfully',
        variant: 'success',
      });
      router.push(`/dashboard/event/${showId}/exhibitors`);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update exhibitor',
        variant: 'destructive',
      });
    },
  });

  const onSubmit = (data: ShowExhibitorCreateFormData) => {
    // Convert booth number string to array
    const boothNumberArray = data.boothNumber
      ? data.boothNumber
          .split(',')
          .map((booth) => booth.trim())
          .filter((booth) => booth.length > 0)
      : [];

    if (isAdd) {
      if (!data.companyId && !createNewCompany) {
        toast({
          title: 'Error',
          description: 'Please select a company or create a new one',
          variant: 'destructive',
        });
        return;
      }

      if (createNewCompany && !data.companyId) {
        toast({
          title: 'Error',
          description: 'Please complete company creation first',
          variant: 'destructive',
        });
        return;
      }

      const payload = {
        showId: data.showId,
        companyId: parseInt(data.companyId!),
        boothNumber: boothNumberArray,
        ...(data.useExistingContact
          ? { contactId: parseInt(data.contactId!) }
          : {
              contactTypeId: parseInt(data.contactTypeId!),
              firstName: data.firstName,
              lastName: data.lastName,
              email: data.email,
              telephone: data.telephone,
              ext: data.ext,
              cellphone: data.cellphone,
              sendEmailInvite: data.sendEmailInvite,
            }),
      };
      createMutation.mutate(payload);
    } else {
      // For update, send all form data to support different update scenarios
      const updatePayload: any = {
        boothNumber: boothNumberArray,
        isActive: true, // You might want to add this to the form
      };

      // If company changed, include it
      if (data.companyId && data.companyId !== defaultValues?.companyId) {
        updatePayload.companyId = parseInt(data.companyId);
      }

      // Handle contact updates based on form state
      if (data.useExistingContact) {
        // Scenario 1: Change to existing contact
        if (data.contactId !== defaultValues?.contactId) {
          updatePayload.contactId = parseInt(data.contactId!);
        }
      } else {
        // Scenario 2: Create new contact or update existing contact info
        updatePayload.contactId = null; // This will create a new contact
        updatePayload.contactTypeId = parseInt(data.contactTypeId);
        updatePayload.firstName = data.firstName;
        updatePayload.lastName = data.lastName;
        updatePayload.email = data.email;
        updatePayload.telephone = data.telephone;
        updatePayload.ext = data.ext;
        updatePayload.cellphone = data.cellphone;
        updatePayload.sendEmailInvite = data.sendEmailInvite;
      }

      updateMutation.mutate(updatePayload);
    }
  };

  const handleCancel = () => {
    router.push(`/dashboard/event/${showId}/exhibitors`);
  };

  // Handle company creation success
  const handleCompanyCreateSuccess = (companyId?: number) => {
    if (companyId) {
      // Set the newly created company as selected
      form.setValue('companyId', companyId.toString());
    }
    setCreateNewCompany(false);
    setShowCompanyEdit(false);
  };

  // Handle company creation cancel
  const handleCompanyCreateCancel = () => {
    setCreateNewCompany(false);
    setShowCompanyEdit(false);
  };

  return (
    <div className="w-full">
      {showCompanyEdit ? (
        // Show company form outside of main form to avoid nesting
        <CompanyForm
          id={
            isAdd && createNewCompany
              ? undefined
              : parseInt(form.watch('companyId')!)
          }
          onCancel={
            isAdd && createNewCompany
              ? handleCompanyCreateCancel
              : () => setShowCompanyEdit(false)
          }
          onSuccess={
            isAdd && createNewCompany
              ? handleCompanyCreateSuccess
              : () => setShowCompanyEdit(false)
          }
        />
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Company Information */}
            <Card>
              <CardHeader>
                <CardTitle>Company Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <>
                  <Field
                    control={form.control}
                    name="companyId"
                    label="Company"
                    type={{
                      type: 'select',
                      props: {
                        options:
                          companies
                            ?.filter((company) => !company.isArchived)
                            ?.map((company) => ({
                              value: company.id.toString(),
                              label: company.name,
                            })) ?? [],
                        placeholder: 'Select a company',
                      },
                    }}
                    required={!createNewCompany}
                  />

                  {/* Create New Company Checkbox - Only show in add mode */}
                  {isAdd && (
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="createNewCompany"
                        checked={createNewCompany}
                        onChange={(e) => {
                          const checked = e.target.checked;
                          setCreateNewCompany(checked);
                          if (checked) {
                            setShowCompanyEdit(true);
                            form.setValue('companyId', ''); // Clear company selection
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                      <label
                        htmlFor="createNewCompany"
                        className="text-sm font-medium"
                      >
                        Create new company
                      </label>
                    </div>
                  )}

                  {/* Edit Company Checkbox - Only show in edit mode when company is selected */}
                  {!isAdd && form.watch('companyId') && (
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="editCompany"
                        checked={editCompany}
                        onChange={(e) => {
                          const checked = e.target.checked;
                          setEditCompany(checked);
                          if (checked) {
                            setShowCompanyEdit(true);
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                      <label
                        htmlFor="editCompany"
                        className="text-sm font-medium"
                      >
                        Edit company
                      </label>
                    </div>
                  )}

                  <Field
                    control={form.control}
                    name="boothNumber"
                    label="Booth Number"
                    type="tags"
                  />
                </>
              </CardContent>
            </Card>

            {/* Contact Information - Only show when company is selected/created and not editing company */}
            {isAdd &&
              (form.watch('companyId') || createNewCompany) &&
              !showCompanyEdit && (
                <ContactForm
                  control={form.control}
                  selectedCompanyId={
                    form.watch('companyId')
                      ? parseInt(form.watch('companyId')!)
                      : null
                  }
                  useExistingContact={form.watch('useExistingContact')}
                />
              )}

            {/* Contact Information - Always show in edit mode when not editing company */}
            {!isAdd && !showCompanyEdit && (
              <ContactForm
                control={form.control}
                selectedCompanyId={
                  form.watch('companyId')
                    ? parseInt(form.watch('companyId')!)
                    : null
                }
                useExistingContact={form.watch('useExistingContact')}
              />
            )}

            {/* Action Buttons - Hide when editing company */}
            {!showCompanyEdit && (
              <div className="flex justify-end gap-4 pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  iconName="CancelIcon"
                  iconProps={{ size: 20 }}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="main"
                  disabled={
                    createMutation.isPending || updateMutation.isPending
                  }
                  iconName={
                    createMutation.isPending || updateMutation.isPending
                      ? 'LoadingIcon'
                      : 'SaveIcon'
                  }
                  iconProps={{ size: 20, className: 'text-white' }}
                >
                  {createMutation.isPending || updateMutation.isPending
                    ? isAdd
                      ? 'Creating...'
                      : 'Updating...'
                    : isAdd
                      ? 'Create Exhibitor'
                      : 'Update Exhibitor'}
                </Button>
              </div>
            )}
          </form>
        </Form>
      )}
    </div>
  );
}

interface ExhibitorFormProps {
  showId: number;
  exhibitorId?: number;
  isAdd: boolean;
}

export default function ExhibitorForm({
  showId,
  exhibitorId,
  isAdd,
}: ExhibitorFormProps) {
  // Fetch exhibitor data if editing
  const { data: exhibitor, isLoading: isLoadingExhibitor } = useQuery({
    queryKey: [ShowExhibitorsQuery.tags, exhibitorId],
    queryFn: () => ShowExhibitorsQuery.getById(exhibitorId!),
    enabled: !isAdd && !!exhibitorId,
  });

  // Prepare default values for editing
  const defaultValues =
    exhibitor && !isAdd
      ? {
          showId: exhibitor.showId,
          companyId: exhibitor.companyId.toString(),
          contactId: exhibitor.contactId.toString(),
          boothNumber: exhibitor.boothNumber
            ? exhibitor.boothNumber.join(', ')
            : '',
          useExistingContact: true,
          createNewCompany: false,
          contactTypeId: exhibitor.contactTypeId.toString(),
          firstName: exhibitor.contactFirstName,
          lastName: exhibitor.contactLastName,
          email: exhibitor.contactEmail,
          telephone: exhibitor.contactTelephone,
          ext: exhibitor.contactExt,
          cellphone: exhibitor.contactCellphone,
          sendEmailInvite: true,
        }
      : undefined;

  return (
    <Suspense isLoading={!isAdd && isLoadingExhibitor}>
      <FormContent
        defaultValues={defaultValues}
        showId={showId}
        exhibitorId={exhibitorId}
        isAdd={isAdd}
      />
    </Suspense>
  );
}
