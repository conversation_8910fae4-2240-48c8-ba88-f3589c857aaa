// Base Service interface
export interface Service {
  id: number;
  name: string;
  description: string;
  serviceFormType: ServiceFormType;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Service form types enum based on the screenshots
export enum ServiceFormType {
  JANITORIAL_SERVICES = 'JAN<PERSON>ORIAL_SERVICES',
  LABOUR_INSTALLATION_DISMANTLE = 'LABOUR_INSTALLATION_DISMANTLE',
  LABOUR_BY_TIME_RANGE = 'LABOUR_BY_TIME_RANGE',
  ON_SITE_MATERIAL_HANDLING = 'ON_SITE_MATERIAL_HANDLING',
  PORTER_SERVICE = 'PORTER_SERVICE',
  MANDATORY_STORAGE_SERVICE = 'MANDATORY_STORAGE_SERVICE',
  FORKLIFT_SERVICE = 'FORKLIFT_SERVICE',
  FORKLIFT_BY_TIME_RANGE = 'FORKLIFT_BY_TIME_RANGE',
  CART_DOLLY_MATERIAL_HANDLING = 'CART_DOLLY_MATERIAL_HANDLING',
  PRE_POST_SHOW_SERVICES = 'PRE_POST_SHOW_SERVICES',
  ADVANCE_MATERIAL_HANDLING = 'ADVANCE_MATERIAL_HANDLING',
  LOCAL_CARTAGE = 'LOCAL_CARTAGE',
  CUSTOM_BROKERAGE = 'CUSTOM_BROKERAGE',
  GROUND_TRANSPORTATION = 'GROUND_TRANSPORTATION',
  POST_SHOW_STORAGE = 'POST_SHOW_STORAGE',
}

// Show Service Selection interface
export interface ShowService {
  id: number;
  showId: number;
  serviceId: number;
  isSelected: boolean;
  formData?: ServiceFormData;
  createdAt: string;
  updatedAt: string;
  // Populated fields
  service: Service;
}

// Base interface for all service form data
export interface ServiceFormData {
  serviceId: number;
  showId: number;
  formType: ServiceFormType;
  data: Record<string, any>;
}

// Janitorial Services Form Data
export interface JanitorialServicesFormData {
  serviceIncludes: string;
  rateType: 'sq_ft' | 'hourly' | 'daily';
  oneTimeRate: number;
  twoDaysRate: number;
  threeDaysRate: number;
}

// Labour (Installation and Dismantle) Form Data
export interface LabourInstallationDismantleFormData {
  serviceIncludes: string;
  minimumHour: '1_hour' | '2_hours';
  regularTimeRate: number;
  minimumCharge: number;
  daysHours: string;
  overtimeRate: number;
  overtimeMinimumCharge: number;
  overtimeDaysHours: string;
  doubleTimeRate: number;
  doubleTimeMinimumCharge: number;
  doubleTimeDaysHours: string;
  supervisionRate: number;
  supervisorNotes: string;
  ladderPrices: {
    sixFeet: number;
    eightFeet: number;
    tenFeet: number;
    twelveFeet: number;
  };
}

// Labour by Time Range Form Data
export interface LabourByTimeRangeFormData {
  shift: {
    regularTime: {
      from: string;
      to: string;
      weekdays: number;
      weekends: number;
    };
    overtime: {
      from: string;
      to: string;
      weekdays: number;
      weekends: number;
    };
    doubleTime: {
      from: string;
      to: string;
      weekdays: number;
      weekends: number;
    };
  };
  supervisionRate: number;
  ladderPrice: {
    sixFeet: number;
    eightFeet: number;
    tenFeet: number;
    twelveFeet: number;
  };
  installation: {
    minimumCharge: number;
    minimumHour: number;
  };
  dismantle: {
    minimumCharge: number;
    minimumHour: number;
  };
}

// On-Site Material Handling Form Data
export interface OnSiteMaterialHandlingFormData {
  receivingAddress: string;
  daysHours: string;
  pricePerWeight: number;
  weight: number;
  weightUnit: 'lbs' | 'kg';
  minimumCharge: number;
}

// Porter Service Form Data
export interface PorterServiceFormData {
  minimumHours: number;
  minimumDays: number;
  minimumLabourers: number;
  rateType: 'sq_ft' | 'hourly' | 'daily';
  rates: {
    rate: number;
    exhibitArea: number;
  }[];
}

// Mandatory Storage Service Form Data
export interface MandatoryStorageServiceFormData {
  // This is a simple service with no additional fields
}

// Forklift Service Form Data
export interface ForkliftServiceFormData {
  rates: {
    weight: number;
    regularRate: number;
    overtimeRate: number;
    doubleRate: number;
  }[];
  daysHours: {
    regular: string;
    regularMinimumCharge: number;
    overtime: string;
    overtimeMinimumCharge: number;
    doubleTime: string;
    doubleTimeMinimumCharge: number;
  };
  additionalServices: {
    manCageRate: number;
    boomRate: number;
  };
}

// Forklift by Time Range Form Data
export interface ForkliftByTimeRangeFormData {
  weightRanges: {
    range1: {
      min: number;
      max: number;
      shift: {
        regularTime: {
          from: string;
          to: string;
          weekdays: number;
          weekends: number;
        };
        overtime: {
          from: string;
          to: string;
          weekdays: number;
          weekends: number;
        };
        doubleTime: {
          from: string;
          to: string;
          weekdays: number;
          weekends: number;
        };
      };
    };
    range2: {
      min: number;
      max: number;
      shift: {
        regularTime: {
          from: string;
          to: string;
          weekdays: number;
          weekends: number;
        };
        overtime: {
          from: string;
          to: string;
          weekdays: number;
          weekends: number;
        };
        doubleTime: {
          from: string;
          to: string;
          weekdays: number;
          weekends: number;
        };
      };
    };
    range3: {
      min: number;
      max: number;
      shift: {
        regularTime: {
          from: string;
          to: string;
          weekdays: number;
          weekends: number;
        };
        overtime: {
          from: string;
          to: string;
          weekdays: number;
          weekends: number;
        };
        doubleTime: {
          from: string;
          to: string;
          weekdays: number;
          weekends: number;
        };
      };
    };
  };
  moveIn: {
    minimumCharge: number;
    minimumHour: number;
  };
  moveOut: {
    minimumCharge: number;
    minimumHour: number;
  };
  additionalServices: {
    manCageRate: number;
    boomRate: number;
  };
}

// Cart/Dolly Material Handling Service Form Data
export interface CartDollyMaterialHandlingFormData {
  rentalRate: number;
}

// Pre & Post Show Services Form Data
export interface PrePostShowServicesFormData {
  serviceToShawCenter: boolean;
  receivingAddress: string;
  daysHours: string;
  receivingStartDate: string;
  cutoffDeadlineDate: string;
  minimumCharge: number;
  weightType: 'lbs' | 'kg';
  cratedSkidded: {
    price: number;
    perWeight: number;
  };
  specialHandling: {
    price: number;
    perWeight: number;
  };
  uncratedPadWrapped: {
    price: number;
    perWeight: number;
  };
}

// Union type for all form data types
export type AllServiceFormData =
  | JanitorialServicesFormData
  | LabourInstallationDismantleFormData
  | LabourByTimeRangeFormData
  | OnSiteMaterialHandlingFormData
  | PorterServiceFormData
  | MandatoryStorageServiceFormData
  | ForkliftServiceFormData
  | ForkliftByTimeRangeFormData
  | CartDollyMaterialHandlingFormData
  | PrePostShowServicesFormData;

// Request interfaces
export interface CreateShowServiceRequest {
  showId: number;
  serviceId: number;
  isSelected: boolean;
  formData?: ServiceFormData;
}

export interface UpdateShowServiceRequest {
  isSelected: boolean;
  formData?: ServiceFormData;
}
