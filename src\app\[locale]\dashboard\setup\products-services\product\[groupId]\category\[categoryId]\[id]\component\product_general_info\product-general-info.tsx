'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import {
  OfferingCreateData,
  OfferingCreateSchema,
} from '@/schema/OfferingSchema';
import OfferingQuery from '@/services/queries/OfferingQuery';
import TaxQuery from '@/services/queries/TaxQuery';
import UnitQuery from '@/services/queries/UnitQuery';
import { DropzoneOptions } from 'react-dropzone';
import { getQueryClient } from '@/utils/query-client';
import { ChevronRight } from 'lucide-react';

interface OfferingGeneralInfoProps {
  id?: number;
  groupId: number;
  categoryId: number;
}

function FormContent({
  defaultValues,
  id,
  groupId,
  categoryId,
}: {
  groupId: number;
  categoryId: number;
  defaultValues?: OfferingCreateData;
  id?: number;
}) {
  const { push } = useRouter();
  const { toast } = useToast();

  const dropzone = {
    accept: {
      'image/*': ['.jpg', '.jpeg', '.png'],
    },
    multiple: false,
    maxFiles: 1,
  } satisfies DropzoneOptions;

  const form = useForm<OfferingCreateData>({
    resolver: zodResolver(OfferingCreateSchema),

    defaultValues: defaultValues ?? {
      categoryId: categoryId.toString(),
      groupTypeId: groupId.toString(),
      name: '',
      code: '',
      unitChargedId: '',
      supplierItemNumber: '',
      publicDescription: '',
      internalDescription: '',
      displayOrder: '',
      isUnitTypeEach: false,
      isAddOn: false,
      isForSmOnly: false,
      isInternalOnly: false,
      image: undefined,
      isActive: true,
      isObsolete: false,
      taxType: [],
    },
  });

  const { data: taxType, isLoading: isLoadingTaxType } = useQuery({
    queryKey: TaxQuery.tags,
    queryFn: TaxQuery.getTaxType,
  });

  const { data: unit, isLoading: isLoadingUnit } = useQuery({
    queryKey: UnitQuery.tags,
    queryFn: UnitQuery.getUnit,
  });

  const { mutate, isPending } = useMutation({
    mutationFn: id
      ? (data: OfferingCreateData) => OfferingQuery.update(id, data)
      : (data: OfferingCreateData) => OfferingQuery.add(data),
    onSuccess: async (newId) => {
      if (id) {
        await getQueryClient().invalidateQueries({
          queryKey: OfferingQuery.tags,
        });
        await getQueryClient().invalidateQueries({
          queryKey: ['Offering', { id }],
        });
      }

      toast({
        title: 'Success',
        description: id
          ? 'Product updated successfully.'
          : 'Product created successfully.',
        variant: 'success',
      });

      const productId = id || newId;
      if (productId) {
        push(
          `/dashboard/setup/products-services/product/${groupId}/category/${categoryId}/${id}/description`,
        );
      } else {
        push(`/dashboard/setup/products-services/product`);
      }
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Something went wrong.',
        variant: 'destructive',
      });
    },
  });
  const isActive = form.watch('isActive');
  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => mutate(data))}
        className="space-y-4"
      >
        <h2 className="text-xl font-semibold text-[#00646C] border-b border-slate-200 pb-3">
          Product Information
        </h2>
        <Field
          control={form.control}
          name="name"
          label="Product Name"
          placeholder="Enter product name"
          type="text"
          required
        />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-32 gap-y-2">
          <Field
            control={form.control}
            name="supplierItemNumber"
            label="Supplier Item Number"
            placeholder="Enter item number"
            type="text"
          />
          <Field
            control={form.control}
            name="displayOrder"
            label="Display Order"
            placeholder="Enter display order"
            type="text"
          />
        </div>
        <Field
          control={form.control}
          name="image"
          label="Product Image"
          required={id ? false : true}
          type={{
            type: 'file',
            props: {
              dropzoneOptions: dropzone,
            },
          }}
          placeholder="Upload offering image"
        />
        {isLoadingTaxType ? (
          <Spinner />
        ) : (
          taxType &&
          taxType.length > 0 && (
            <Field
              control={form.control}
              name="taxType"
              label="Tax Type"
              type={{
                type: 'multiSelect',
                props: {
                  options:
                    taxType?.map((tax) => ({
                      label: `${tax.name}`,
                      value: tax.id.toString(),
                    })) || [],
                  placeholder: 'Select Tax',
                },
              }}
            />
          )
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-32 gap-y-2">
          {isLoadingUnit ? (
            <Spinner />
          ) : (
            unit &&
            unit.length > 0 && (
              <Field
                control={form.control}
                name="unitChargedId"
                label="Unit Charged as"
                type={{
                  type: 'select',
                  props: {
                    options:
                      unit.map((u) => ({
                        label: `${u.name}`,
                        value: u.id.toString(),
                      })) ?? [],
                    placeholder: 'Select unit type',
                  },
                }}
              />
            )
          )}
          <Field
            control={form.control}
            name="isUnitTypeEach"
            label="Unit Type (Each)"
            type="checkbox"
          />
          <Field
            control={form.control}
            name="isAddOn"
            label="Add-on Product"
            type="checkbox"
          />
          <Field
            control={form.control}
            name="isForSmOnly"
            label="Show Manager Only"
            type="checkbox"
          />
          <Field
            control={form.control}
            name="isInternalOnly"
            label="Internal Only"
            type="checkbox"
          />
          <Field
            control={form.control}
            name="isActive"
            label="Active"
            type="checkbox"
          />
          {!isActive && (
            <Field
              control={form.control}
              name="isObsolete"
              label="Obsolete"
              type="checkbox"
            />
          )}
        </div>

        <div className="flex justify-between pt-6 border-t border-slate-200">
          <Button
            type="button"
            variant="outline"
            onClick={() => push('/dashboard/setup/products-services/product')}
          >
            Cancel
          </Button>
          <Button variant="main" type="submit" disabled={isPending}>
            {isPending ? <Spinner className="mr-2 text-white" /> : null}
            Save & Continue
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default function OfferingGeneralInfo({
  id,
  groupId,
  categoryId,
}: OfferingGeneralInfoProps) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Offering', { id }],
    queryFn: () => OfferingQuery.getById(id!),
    enabled: !!id,
    select: (res): OfferingCreateData => ({
      name: res.name ?? '',
      supplierItemNumber: res.supplierItemNumber ?? '',
      categoryId: res.categoryId?.toString() ?? '',
      groupTypeId: res.groupTypeId?.toString() ?? '',
      displayOrder: res.displayOrder?.toString() ?? '',
      publicDescription: res.publicDescription ?? '',
      internalDescription: res.internalDescription ?? '',
      imagePath: res.imagePath ?? '',
      code: res.code ?? '',
      unitChargedId: res.unitChargedId?.toString() ?? '',
      image: res.image,
      isUnitTypeEach: res.isUnitTypeEach ?? false,
      isActive: res.isActive ?? true,
      isObsolete: res.isObsolete ?? false,
      isAddOn: res.isAddOn ?? false,
      isForSmOnly: res.isForSmOnly ?? false,
      isInternalOnly: res.isInternalOnly ?? false,
      taxType: res.taxTypeIds?.map((i) => i.toString()) ?? [],
    }),
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <FormContent
        defaultValues={data}
        id={id}
        groupId={groupId}
        categoryId={categoryId}
      />
    </Suspense>
  );
}
