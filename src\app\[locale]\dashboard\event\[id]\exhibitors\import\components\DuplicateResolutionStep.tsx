'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/Badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  AlertCircle,
  Users,
  Building,
  Mail,
  Phone,
  ChevronRight,
  Loader2,
  FileSpreadsheet,
  Database,
  Edit3,
  CheckCircle2,
  Info,
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import ExhibitorImportQuery from '@/services/queries/ExhibitorImportQuery';
import type {
  ExhibitorImportDuplicateDto,
  ExhibitorImportResolveDto,
  ExhibitorImportDuplicateResolutionDto,
} from '@/models/ExhibitorImport';

interface DuplicateResolutionStepProps {
  sessionId: string;
  duplicates: ExhibitorImportDuplicateDto[];
  onResolved: () => void;
  isLoading: boolean;
}

interface FieldResolution {
  fieldName: string;
  selectedSource: 'Excel' | 'Database' | 'Custom';
  selectedValue: string;
  excelValue: string;
  databaseValue: string;
  customValue?: string;
}

const DuplicateResolutionStep: React.FC<DuplicateResolutionStepProps> = ({
  sessionId,
  duplicates,
  onResolved,
  isLoading: parentLoading,
}) => {
  const [isResolving, setIsResolving] = useState(false);
  const [resolutions, setResolutions] = useState<
    Record<number, FieldResolution[]>
  >({});

  const isLoading = parentLoading || isResolving;

  // Initialize resolutions for each duplicate
  React.useEffect(() => {
    console.log('Duplicates received:', duplicates);
    const initialResolutions: Record<number, FieldResolution[]> = {};

    duplicates.forEach((duplicate) => {
      initialResolutions[duplicate.duplicateId] = duplicate.fieldConflicts.map(
        (conflict) => ({
          fieldName: conflict.fieldName,
          selectedSource: 'Excel',
          selectedValue: conflict.excelValue,
          excelValue: conflict.excelValue,
          databaseValue: conflict.databaseValue,
        }),
      );
    });

    console.log('Setting initial resolutions:', initialResolutions);
    setResolutions(initialResolutions);
  }, [duplicates]);

  const updateFieldResolution = (
    duplicateId: number,
    fieldName: string,
    source: 'Excel' | 'Database' | 'Custom',
    customValue?: string,
  ) => {
    setResolutions((prev) => {
      const duplicateResolutions = prev[duplicateId] || [];
      const fieldIndex = duplicateResolutions.findIndex(
        (r) => r.fieldName === fieldName,
      );

      if (fieldIndex >= 0) {
        const field = duplicateResolutions[fieldIndex];
        const updatedField: FieldResolution = {
          ...field,
          selectedSource: source,
          selectedValue:
            source === 'Excel'
              ? field.excelValue
              : source === 'Database'
                ? field.databaseValue
                : customValue || '',
          customValue: source === 'Custom' ? customValue : undefined,
        };

        const newResolutions = [...duplicateResolutions];
        newResolutions[fieldIndex] = updatedField;

        return {
          ...prev,
          [duplicateId]: newResolutions,
        };
      }

      return prev;
    });
  };

  const handleResolveAll = async () => {
    console.log('handleResolveAll called');
    setIsResolving(true);

    try {
      const duplicateResolutions: ExhibitorImportDuplicateResolutionDto[] =
        duplicates.map((duplicate) => ({
          duplicateId: duplicate.duplicateId,
          fieldResolutions: (resolutions[duplicate.duplicateId] || []).map(
            (resolution) => ({
              fieldName: resolution.fieldName,
              selectedSource: resolution.selectedSource,
              selectedValue: resolution.selectedValue,
              excelValue: resolution.excelValue,
              databaseValue: resolution.databaseValue,
              customValue: resolution.customValue,
            }),
          ),
        }));

      const request: ExhibitorImportResolveDto = {
        sessionId,
        duplicateResolutions,
      };

      console.log('Resolving duplicates with request:', request);
      const response = await ExhibitorImportQuery.resolve(request);
      console.log('Duplicate resolution response:', response);

      toast({
        title: 'Duplicates resolved successfully',
        description:
          'All duplicate conflicts have been resolved. Ready to proceed with import.',
      });

      onResolved();
    } catch (error) {
      toast({
        title: 'Failed to resolve duplicates',
        description:
          error instanceof Error
            ? error.message
            : 'An error occurred while resolving duplicates',
        variant: 'destructive',
      });
    } finally {
      setIsResolving(false);
    }
  };

  const getFieldIcon = (fieldName: string) => {
    if (fieldName.toLowerCase().includes('email'))
      return <Mail className="h-4 w-4" />;
    if (fieldName.toLowerCase().includes('phone'))
      return <Phone className="h-4 w-4" />;
    if (fieldName.toLowerCase().includes('company'))
      return <Building className="h-4 w-4" />;
    return <Users className="h-4 w-4" />;
  };

  const formatFieldName = (fieldName: string) => {
    return fieldName
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .trim();
  };

  if (duplicates.length === 0) {
    return (
      <div className="text-center space-y-6">
        <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircle2 className="h-8 w-8 text-green-600" />
        </div>
        <div>
          <h2 className="text-2xl font-semibold text-green-800">
            No Duplicates Found
          </h2>
          <p className="text-muted-foreground mt-2">
            Great! No duplicate conflicts were detected. Your data is ready for
            import.
          </p>
        </div>
        <Button
          onClick={onResolved}
          size="lg"
          className="bg-green-600 hover:bg-green-700"
        >
          Proceed to Import
          <ChevronRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="mx-auto w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center">
          <AlertCircle className="h-8 w-8 text-orange-600" />
        </div>
        <div>
          <h2 className="text-2xl font-semibold">
            Resolve Duplicate Conflicts
          </h2>
          <p className="text-muted-foreground">
            We found {duplicates.length} duplicate conflict
            {duplicates.length > 1 ? 's' : ''} that need your attention. Choose
            how to handle each conflict below.
          </p>
        </div>
      </div>

      {/* Instructions */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="space-y-2">
              <h4 className="font-medium text-blue-800">
                How to resolve conflicts:
              </h4>
              <div className="text-sm text-blue-700 space-y-1">
                <div className="flex items-center space-x-2">
                  <FileSpreadsheet className="h-4 w-4" />
                  <span>
                    <strong>Excel Value:</strong> Use the value from your
                    uploaded file
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Database className="h-4 w-4" />
                  <span>
                    <strong>Database Value:</strong> Keep the existing value in
                    the system
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Edit3 className="h-4 w-4" />
                  <span>
                    <strong>Custom Value:</strong> Enter a new value manually
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Tips */}
      <Alert className="border-yellow-200 bg-yellow-50">
        <Info className="h-4 w-4 text-yellow-600" />
        <AlertDescription className="text-yellow-800">
          <strong>💡 Quick Tips:</strong> Excel values are from your uploaded
          file, Database values are what's currently in the system. Choose Excel
          if the uploaded data is more recent, or Database if you want to keep
          existing information.
        </AlertDescription>
      </Alert>

      {/* Conflicts */}
      <div className="space-y-6">
        {duplicates.map((duplicate, index) => (
          <Card
            key={duplicate.duplicateId}
            className="border-l-4 border-l-orange-500 shadow-sm"
          >
            <CardHeader className="bg-gradient-to-r from-orange-50 to-transparent">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center text-orange-700 font-bold text-sm">
                    {index + 1}
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-orange-800">
                      {duplicate.duplicateType} Conflict
                    </div>
                    <div className="text-sm font-normal text-muted-foreground">
                      {duplicate.conflictDescription}
                    </div>
                  </div>
                </CardTitle>
                <Badge
                  variant="outline"
                  className="bg-orange-100 text-orange-800 border-orange-300"
                >
                  {duplicate.duplicateValue}
                </Badge>
              </div>
              <div className="mt-2 text-sm text-muted-foreground">
                <strong>Affected Rows:</strong>{' '}
                {duplicate.rowNumbers.join(', ')}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {duplicate.fieldConflicts.map((conflict) => {
                const resolution = resolutions[duplicate.duplicateId]?.find(
                  (r) => r.fieldName === conflict.fieldName,
                );

                return (
                  <div
                    key={conflict.fieldName}
                    className="border-2 border-gray-200 rounded-lg p-4 bg-gray-50"
                  >
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="p-2 bg-white rounded-lg shadow-sm">
                        {getFieldIcon(conflict.fieldName)}
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800">
                          {formatFieldName(conflict.fieldName)}
                        </h4>
                        <p className="text-sm text-gray-600">
                          Choose which value to keep
                        </p>
                      </div>
                    </div>

                    <RadioGroup
                      value={resolution?.selectedSource || 'Excel'}
                      onValueChange={(value) =>
                        updateFieldResolution(
                          duplicate.duplicateId,
                          conflict.fieldName,
                          value as any,
                        )
                      }
                      className="space-y-3"
                    >
                      {/* Excel Value Option */}
                      <div
                        className={`border-2 rounded-lg p-3 cursor-pointer transition-all ${
                          resolution?.selectedSource === 'Excel'
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 bg-white hover:border-blue-300'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <RadioGroupItem
                            value="Excel"
                            id={`excel-${duplicate.duplicateId}-${conflict.fieldName}`}
                          />
                          <FileSpreadsheet className="h-4 w-4 text-blue-600" />
                          <Label
                            htmlFor={`excel-${duplicate.duplicateId}-${conflict.fieldName}`}
                            className="flex-1 cursor-pointer"
                          >
                            <div className="flex justify-between items-center">
                              <div>
                                <div className="font-medium text-blue-800">
                                  Excel Value
                                </div>
                                <div className="text-xs text-blue-600">
                                  From your uploaded file
                                </div>
                              </div>
                              <code className="bg-blue-100 text-blue-800 px-3 py-1 rounded-md text-sm font-mono">
                                {conflict.excelValue || '(empty)'}
                              </code>
                            </div>
                          </Label>
                        </div>
                      </div>

                      {/* Database Value Option */}
                      <div
                        className={`border-2 rounded-lg p-3 cursor-pointer transition-all ${
                          resolution?.selectedSource === 'Database'
                            ? 'border-green-500 bg-green-50'
                            : 'border-gray-200 bg-white hover:border-green-300'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <RadioGroupItem
                            value="Database"
                            id={`db-${duplicate.duplicateId}-${conflict.fieldName}`}
                          />
                          <Database className="h-4 w-4 text-green-600" />
                          <Label
                            htmlFor={`db-${duplicate.duplicateId}-${conflict.fieldName}`}
                            className="flex-1 cursor-pointer"
                          >
                            <div className="flex justify-between items-center">
                              <div>
                                <div className="font-medium text-green-800">
                                  Database Value
                                </div>
                                <div className="text-xs text-green-600">
                                  Current value in system
                                </div>
                              </div>
                              <code className="bg-green-100 text-green-800 px-3 py-1 rounded-md text-sm font-mono">
                                {conflict.databaseValue || '(empty)'}
                              </code>
                            </div>
                          </Label>
                        </div>
                      </div>

                      {/* Custom Value Option */}
                      <div
                        className={`border-2 rounded-lg p-3 cursor-pointer transition-all ${
                          resolution?.selectedSource === 'Custom'
                            ? 'border-purple-500 bg-purple-50'
                            : 'border-gray-200 bg-white hover:border-purple-300'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <RadioGroupItem
                            value="Custom"
                            id={`custom-${duplicate.duplicateId}-${conflict.fieldName}`}
                          />
                          <Edit3 className="h-4 w-4 text-purple-600" />
                          <Label
                            htmlFor={`custom-${duplicate.duplicateId}-${conflict.fieldName}`}
                            className="flex-1 cursor-pointer"
                          >
                            <div className="space-y-2 w-full">
                              <div>
                                <div className="font-medium text-purple-800">
                                  Custom Value
                                </div>
                                <div className="text-xs text-purple-600">
                                  Enter your own value
                                </div>
                              </div>
                              {resolution?.selectedSource === 'Custom' && (
                                <Input
                                  placeholder="Enter custom value..."
                                  value={resolution.customValue || ''}
                                  onChange={(e) =>
                                    updateFieldResolution(
                                      duplicate.duplicateId,
                                      conflict.fieldName,
                                      'Custom',
                                      e.target.value,
                                    )
                                  }
                                  className="mt-2 border-purple-300 focus:border-purple-500"
                                />
                              )}
                            </div>
                          </Label>
                        </div>
                      </div>
                    </RadioGroup>
                  </div>
                );
              })}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Progress Summary */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <CheckCircle2 className="h-5 w-5 text-green-600" />
              <div>
                <div className="font-medium text-green-800">
                  Resolution Progress
                </div>
                <div className="text-sm text-green-600">
                  {duplicates.length} conflict{duplicates.length > 1 ? 's' : ''}{' '}
                  ready to be resolved
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-green-700">
                {duplicates.length}
              </div>
              <div className="text-xs text-green-600">Total Conflicts</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Debug Info */}
      <Card className="bg-gray-50 border-gray-200">
        <CardContent className="p-4">
          <div className="text-sm text-gray-600">
            <strong>Debug Info:</strong>
            <div>Duplicates count: {duplicates.length}</div>
            <div>Resolutions count: {Object.keys(resolutions).length}</div>
            <div>Is Loading: {isLoading.toString()}</div>
            <div>Is Resolving: {isResolving.toString()}</div>
          </div>
        </CardContent>
      </Card>

      {/* Test Button */}
      <div className="flex justify-center gap-4 pt-4">
        <Button
          onClick={() => {
            alert('Test button clicked!');
            console.log('Current resolutions:', resolutions);
          }}
          variant="outline"
          size="lg"
        >
          Test Button
        </Button>

        <Button
          onClick={handleResolveAll}
          disabled={isLoading}
          size="lg"
          className="min-w-[250px] bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold py-3 px-8 rounded-lg shadow-lg transition-all duration-200"
        >
          {isResolving ? (
            <>
              <Loader2 className="h-5 w-5 mr-3 animate-spin" />
              Resolving Conflicts...
            </>
          ) : (
            <>
              <CheckCircle2 className="h-5 w-5 mr-3" />
              Resolve All {duplicates.length} Conflict
              {duplicates.length > 1 ? 's' : ''}
              <ChevronRight className="h-5 w-5 ml-3" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default DuplicateResolutionStep;
