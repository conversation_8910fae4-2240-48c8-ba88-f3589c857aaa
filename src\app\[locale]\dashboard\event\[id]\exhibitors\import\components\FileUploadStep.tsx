'use client';

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Upload,
  FileSpreadsheet,
  AlertCircle,
  Loader2,
  Download,
  FileText,
} from 'lucide-react';

interface FileUploadStepProps {
  onFileUpload: (file: File) => void;
  isLoading: boolean;
}

const FileUploadStep: React.FC<FileUploadStepProps> = ({
  onFileUpload,
  isLoading,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [error, setError] = useState<string>('');

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setError('');

    if (rejectedFiles.length > 0) {
      const rejection = rejectedFiles[0];
      if (rejection.errors.some((e: any) => e.code === 'file-too-large')) {
        setError('File is too large. Maximum size is 10MB.');
      } else if (
        rejection.errors.some((e: any) => e.code === 'file-invalid-type')
      ) {
        setError(
          'Invalid file type. Please upload an Excel file (.xlsx or .xls).',
        );
      } else {
        setError('Invalid file. Please try again.');
      }
      return;
    }

    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      setSelectedFile(file);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
        '.xlsx',
      ],
      'application/vnd.ms-excel': ['.xls'],
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024, // 10MB
    disabled: isLoading,
  });

  const handleUpload = () => {
    if (selectedFile) {
      onFileUpload(selectedFile);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setError('');
  };

  const downloadTemplate = async () => {
    // Dynamically import xlsx library
    const XLSX = await import('xlsx');

    // Create sample data for the template
    const sampleData = [
      {
        'Company Name *': 'ABC Corporation',
        'Company Email *': '<EMAIL>',
        'Company Country *': 'Canada',
        'Company Phone': '************',
        'Company Address 1': '123 Business Street',
        'Company Address 2': 'Suite 100',
        'Company City': 'Toronto',
        'Company Province': 'ON',
        'Company Postal Code': 'M5V 3A8',
        'Company Website': 'www.abccorp.com',
        'Contact First Name *': 'John',
        'Contact Last Name *': 'Doe',
        'Contact Email *': '<EMAIL>',
        'Contact Phone': '************',
        'Contact Mobile': '************',
        'Contact Extension': '101',
        'Booth Numbers *': 'A101, A102',
      },
      {
        'Company Name *': 'XYZ Industries',
        'Company Email *': '<EMAIL>',
        'Company Country *': 'Canada',
        'Company Phone': '************',
        'Company Address 1': '456 Industrial Ave',
        'Company Address 2': '',
        'Company City': 'Vancouver',
        'Company Province': 'BC',
        'Company Postal Code': 'V6B 1A1',
        'Company Website': 'www.xyzindustries.com',
        'Contact First Name *': 'Jane',
        'Contact Last Name *': 'Smith',
        'Contact Email *': '<EMAIL>',
        'Contact Phone': '************',
        'Contact Mobile': '************',
        'Contact Extension': '205',
        'Booth Numbers *': 'B201',
      },
    ];

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(sampleData);

    // Set column widths for better readability
    const columnWidths = [
      { wch: 20 }, // Company Name
      { wch: 15 }, // Company Phone
      { wch: 25 }, // Company Email
      { wch: 25 }, // Company Address 1
      { wch: 20 }, // Company Address 2
      { wch: 15 }, // Company City
      { wch: 10 }, // Company Province
      { wch: 12 }, // Company Postal Code
      { wch: 12 }, // Company Country
      { wch: 25 }, // Company Website
      { wch: 15 }, // Contact First Name
      { wch: 15 }, // Contact Last Name
      { wch: 25 }, // Contact Email
      { wch: 15 }, // Contact Phone
      { wch: 15 }, // Contact Mobile
      { wch: 10 }, // Contact Extension
      { wch: 12 }, // Contact Type
      { wch: 15 }, // Booth Numbers
    ];
    worksheet['!cols'] = columnWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Exhibitor Template');

    // Generate and download the file
    XLSX.writeFile(workbook, 'exhibitor_import_template.xlsx');
  };

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold">Upload Exhibitor Data</h2>
        <p className="text-muted-foreground">
          Upload an Excel file containing exhibitor information to begin the
          import process.
        </p>
      </div>

      {/* Sample Template Download */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FileText className="h-8 w-8 text-blue-600" />
              <div>
                <h3 className="font-medium text-blue-900">Need a template?</h3>
                <p className="text-sm text-blue-700">
                  Download our sample template with the correct format and
                  example data
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={downloadTemplate}
              className="border-blue-300 text-blue-700 hover:bg-blue-100"
            >
              <Download className="h-4 w-4 mr-2" />
              Download Template
            </Button>
          </div>

          {/* Template Info */}
          <div className="mt-4 p-3 bg-blue-100 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">
              Template includes:
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-blue-800">
              <div>• Company information (name, contact details, address)</div>
              <div>• Contact person details (name, email, phone)</div>
              <div>• Booth number assignments</div>
              <div>• Sample data for reference</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* File Upload Area */}
      <Card>
        <CardContent className="p-6">
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive
                ? 'border-primary bg-primary/5'
                : selectedFile
                  ? 'border-green-500 bg-green-50'
                  : 'border-muted-foreground/25 hover:border-primary hover:bg-primary/5'
            } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <input {...getInputProps()} />

            {selectedFile ? (
              <div className="space-y-4">
                <FileSpreadsheet className="h-12 w-12 text-green-600 mx-auto" />
                <div>
                  <p className="font-medium text-green-700">
                    {selectedFile.name}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveFile();
                  }}
                  disabled={isLoading}
                >
                  Remove File
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <Upload className="h-12 w-12 text-muted-foreground mx-auto" />
                <div>
                  <p className="text-lg font-medium">
                    {isDragActive
                      ? 'Drop the file here'
                      : 'Drag & drop your Excel file here'}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    or click to browse files
                  </p>
                </div>
                <p className="text-xs text-muted-foreground">
                  Supports .xlsx and .xls files up to 10MB
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Upload Button */}
      <div className="flex justify-center">
        <Button
          onClick={handleUpload}
          disabled={!selectedFile || isLoading}
          size="lg"
          className="min-w-[200px]"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <Upload className="h-4 w-4 mr-2" />
              Upload & Validate
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default FileUploadStep;
