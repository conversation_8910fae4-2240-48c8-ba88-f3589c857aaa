// Configuration for role group specific views
export interface RoleGroupViewConfig {
  roleGroupName: string;
  visibleSections: string[];
}

// Define which sections each role group can see
export const roleGroupViewConfigs: RoleGroupViewConfig[] = [
  {
    roleGroupName: 'Show Manager',
    visibleSections: [
      'promoterInfo',
      'notices',
      'showDetails',
      'workOrders',
      'documents',
      'schedule',
    ],
  },
  {
    roleGroupName: 'Supplier',
    visibleSections: ['showDetails', 'workOrders', 'documents', 'schedule'],
  },
  {
    roleGroupName: 'Exhibitor',
    visibleSections: ['notices', 'showDetails', 'documents', 'schedule'],
  },
  {
    roleGroupName: 'Goodkey Employee',
    visibleSections: [
      'promoterInfo',
      'notices',
      'showDetails',
      'workOrders',
      'documents',
      'schedule',
      'financials',
      'settings',
    ],
  },
  {
    roleGroupName: 'System Supports',
    visibleSections: [
      'promoterInfo',
      'notices',
      'showDetails',
      'workOrders',
      'documents',
      'schedule',
      'financials',
      'settings',
    ],
  },
];

// Default fallback configuration
export const defaultViewConfig: RoleGroupViewConfig = {
  roleGroupName: 'Default',
  visibleSections: ['showDetails'],
};

// Helper function to get view config for a role group
export function getRoleGroupViewConfig(
  roleGroupName?: string,
): RoleGroupViewConfig {
  if (!roleGroupName) return defaultViewConfig;

  const config = roleGroupViewConfigs.find(
    (c) => c.roleGroupName === roleGroupName,
  );
  return config || defaultViewConfig;
}

// Helper function to check if a section is visible for a role group
export function isSectionVisible(
  sectionId: string,
  roleGroupName?: string,
): boolean {
  const config = getRoleGroupViewConfig(roleGroupName);
  return config.visibleSections.includes(sectionId);
}
