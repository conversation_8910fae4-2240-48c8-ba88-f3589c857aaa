import { FileText } from 'lucide-react';
import Image from 'next/image';
import { HTMLInputTypeAttribute, InputHTMLAttributes, ReactNode } from 'react';
import { DropzoneOptions } from 'react-dropzone';
import {
  Control,
  ControllerRenderProps,
  FieldPath,
  FieldValues,
} from 'react-hook-form';

import { cn } from '@/lib/utils';

import FileSvgDraw from '../../file_svg_draw';
import {
  FileInput,
  FileUploader,
  FileUploaderContent,
  FileUploaderItem,
  FileDisplayInfo,
} from '../../FileInput';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../../form';
import Checkbox from '../checkbox';
import { Input } from '../input';
import MultiSelectFormField from '../MultiBadgeSelector';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../select';
import { Textarea } from '../textarea';
import PhoneNumberInput from '../phone_number_input';
import DatePickerInput from '../date_picker_input';

import { RadioGroup, RadioGroupItem } from '../../radio-group';
import { PasswordInput } from '../password-input';
import { Switch } from '../../switch';
import { InputTags } from '../input-tags';
import { TimePicker12Hour } from '../time_picker_input/time-input';
import SimpleEditor from '../../Shad_Rich_Text/simple-editor';
import DatePickerWithRange from '../date_range_picker_input/date-range-picker-input';
import LightSimpleEditor from '../../Shad_Rich_Text/light-simple-editor';
import { TimePickerInput } from '../time_picker_input/time-picker-input';

export type OptionType = { label: string; value: string };
type RenderTypeOption = {
  label: string;
  value: string | number;
  render?: (
    field: ControllerRenderProps<FieldValues, FieldPath<FieldValues>>,
  ) => React.ReactNode;
  isDisabled?: boolean;
};

export interface MultiBadgeSelectOptionType extends OptionType {
  icon?: React.ReactNode;
}

type multiBadgeSelectType = {
  type: 'multiBadgeSelect';
  props: {
    options: MultiBadgeSelectOptionType[];
    placeholder: string;
  };
};
type multiSelectType = {
  type: 'multiSelect';
  props: {
    className?: string;
    options: RenderTypeOption[];
    placeholder?: string;
  };
};
type selectType = {
  type: 'select';
  props: {
    options: OptionType[];
    placeholder: string;
    withNone?: boolean;
  };
};

type RadioGroupType = {
  type: 'RadioGroup';
  props: {
    options: RenderTypeOption[];
    optionContainer?: string;
  };
};

type fileType = {
  type: 'file';
  props: {
    dropzoneOptions: DropzoneOptions;
  };
};
type TimeType = {
  type: 'time' | 'customTime';
  props?: any;
};
export type InputType =
  | HTMLInputTypeAttribute
  | (
      | 'text'
      | 'number'
      | 'phone'
      | 'textarea'
      | 'richText'
      | 'simpleRichText'
      | 'ShadRichText'
      | 'LightRichText'
      | 'checkbox'
      | RadioGroupType
      | 'month'
      | fileType
      | multiBadgeSelectType
      | multiSelectType
      | selectType
      | 'date'
      | 'dateRangePicker'
      | 'switch'
      | 'tags'
      | TimeType
    );

interface IField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> {
  type: InputType;
  control: Control<TFieldValues>;
  name: TName;
  label?: string | ReactNode;
  description?: string | ReactNode;
  required?: boolean;
  containerClassName?: string;
  containerStyle?: React.CSSProperties;
  // uploadImageMutation?: (data: AddRichTextImageData) => Promise<any>;
  [key: string]: any;
}
function Field<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  type,
  name,
  label,
  containerClassName,
  containerStyle,
  description,
  required,
  control,
  // uploadImageMutation,
  ...otherInputProps
}: IField<TFieldValues, TName> &
  Omit<InputHTMLAttributes<HTMLInputElement>, 'type'>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem
          className={cn(
            'grid grid-cols-[150px_1fr] items-center gap-2',
            containerClassName,
          )}
          style={containerStyle}
        >
          {label && (
            <div className="w-full sm:min-w-[100px] sm:max-w-[160px] flex flex-col justify-start">
              <FormLabel
                className={cn('leading-normal font-medium block', {
                  'text-muted-foreground cursor-not-allowed':
                    otherInputProps.disabled,
                })}
              >
                {label}
                {required && <span className="text-red-500"> *</span>}
              </FormLabel>
              {description && <FormDescription>{description}</FormDescription>}
            </div>
          )}
          <div className={cn('flex-1 w-full', !label && 'w-full')}>
            <FormControl>
              {type == 'textarea' ? (
                <Textarea {...field} />
              ) : type == 'phone' ? (
                <PhoneNumberInput {...field} />
              ) : type == 'password' ? (
                <PasswordInput {...field} />
              ) : type == 'date' ? (
                <DatePickerInput
                  onValueChange={field.onChange}
                  date={field.value}
                />
              ) : type == 'dateRangePicker' ? (
                <DatePickerWithRange onValueChange={field.onChange} />
              ) : // type == 'month' ? (
              //   <MonthPicker onValueChange={field.onChange} date={field.value} />
              // ) :
              // type == 'richText' ? (
              //   <PlateController>
              //     <PlateEditor
              //       onChange={(value) => field.onChange(value)}
              //       value={field.value}
              //     />
              //   </PlateController>
              // ) :
              type == 'ShadRichText' ? (
                <SimpleEditor
                  onChange={(value) => {
                    field.onChange(value);
                  }}
                  value={field.value}
                  // uploadImageMutation={uploadImageMutation}
                />
              ) : type == 'LightRichText' ? (
                <LightSimpleEditor
                  onChange={(value) => field.onChange(value)}
                  value={field.value}
                  style={otherInputProps.style}
                  className={otherInputProps.className}
                />
              ) : //  type == 'simpleRichText' ? (
              //   <PlateController>
              //     <SimpleEditor
              //       onChange={(value) => field.onChange(value)}
              //       value={field.value}
              //     />
              //   </PlateController>
              // ) :
              type == 'checkbox' ? (
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  disabled={otherInputProps.disabled}
                ></Checkbox>
              ) : type == 'switch' ? (
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                ></Switch>
              ) : type == 'tags' ? (
                <InputTags
                  value={field.value}
                  onChange={field.onChange}
                  placeholder="Enter values, comma separated..."
                  className="max-w-[500px]"
                />
              ) : typeof type == 'object' ? (
                type.type == 'time' ? (
                  <TimePicker12Hour
                    date={field.value}
                    setDate={(data) => {
                      field.onChange(data);
                    }}
                  />
                ) : type.type == 'customTime' ? (
                  <TimePickerInput
                    picker={type.props?.picker || '12hours'}
                    date={field.value}
                    setDate={field.onChange}
                    {...type.props}
                  />
                ) : type.type == 'file' ? (
                  <FileUploader
                    onValueChange={field.onChange}
                    reSelect={true}
                    {...type.props}
                    {...otherInputProps}
                    value={field.value}
                    className="rounded-sm"
                  >
                    {field.value &&
                    field.value.length > 0 &&
                    Array.isArray(field.value) ? (
                      <FileUploaderContent>
                        {field.value &&
                          field.value.length > 0 &&
                          field.value?.map?.((file: File, i: number) => (
                            <FileUploaderItem key={i} index={i}>
                              <div className="flex items-center gap-3 min-w-0 w-full overflow-hidden">
                                {/* File Preview */}
                                <div className="flex-shrink-0">
                                  {file.type.startsWith('image') ? (
                                    <Image
                                      src={URL.createObjectURL(file)}
                                      alt={file.name}
                                      width={72}
                                      height={72}
                                      className="object-cover rounded-sm size-[72px]"
                                    />
                                  ) : (
                                    <div className="size-[72px] flex items-center justify-center rounded-sm bg-gray-600">
                                      <FileText className="size-full p-4 text-primary" />
                                    </div>
                                  )}
                                </div>

                                {/* File Info with proper truncation */}
                                <FileDisplayInfo
                                  file={file}
                                  className="flex-1 min-w-0 overflow-hidden"
                                />
                              </div>
                            </FileUploaderItem>
                          ))}
                      </FileUploaderContent>
                    ) : (
                      <FileInput className="outline-dashed outline-1 outline-gray-300">
                        <div className="flex items-center justify-center flex-col pt-3 pb-4 w-full ">
                          <FileSvgDraw
                            accept={
                              type.props.dropzoneOptions.accept ?? undefined
                            }
                          />
                        </div>
                      </FileInput>
                    )}
                  </FileUploader>
                ) : type.type == 'multiSelect' ? (
                  <div
                    className={cn(
                      'flex flex-row flex-wrap gap-8 w-full',
                      type.props.className,
                    )}
                  >
                    {type.props.options.map((option, index) => (
                      <FormField
                        key={index}
                        control={control}
                        name={name}
                        render={({ field: checkboxField }) => {
                          return (
                            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                              <FormControl>
                                <label
                                  className={cn(
                                    'checkbox-container flex items-center cursor-pointer relative text-sm',
                                    !option.render && 'flex-row-reverse gap-2 ',
                                  )}
                                >
                                  {!option.render && option.label}
                                  <Checkbox
                                    className={cn(
                                      option.render &&
                                        'absolute right-4 top-4 size-6 z-10',
                                    )}
                                    checked={checkboxField.value?.includes(
                                      option.value,
                                    )}
                                    disabled={option.isDisabled}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? checkboxField.onChange([
                                            ...checkboxField.value,
                                            option.value,
                                          ])
                                        : checkboxField.onChange(
                                            checkboxField.value?.filter(
                                              (v: string) => v !== option.value,
                                            ),
                                          );
                                    }}
                                  ></Checkbox>
                                  {option.render &&
                                    option.render(checkboxField as any)}
                                </label>
                              </FormControl>
                            </FormItem>
                          );
                        }}
                      />
                    ))}
                  </div>
                ) : type.type == 'multiBadgeSelect' ? (
                  <MultiSelectFormField
                    onValueChange={field.onChange}
                    placeholder={type.props.placeholder}
                    options={type.props.options}
                    {...otherInputProps}
                    defaultValue={
                      typeof field.value == 'string' ||
                      typeof field.value == 'number'
                        ? [String(field.value)]
                        : (field.value as any[])?.map(String)
                    }
                  />
                ) : type.type == 'RadioGroup' ? (
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className={cn(
                      'flex flex-row gap-4 flex-wrap w-full',
                      containerClassName,
                    )}
                  >
                    {type.props.options.map((option, index) => (
                      <FormItem
                        key={index}
                        className={cn(
                          'flex flex-row items-center w-fit  relative ',
                          type.props.optionContainer,
                        )}
                      >
                        <FormLabel className="font-normal">
                          {option.label}
                          {option.render && option.render(field as any)}
                        </FormLabel>
                        <FormControl>
                          <RadioGroupItem
                            className={cn(
                              option.render && 'absolute top-4 right-4  z-10 ',
                            )}
                            value={String(option.value)}
                          />
                        </FormControl>
                      </FormItem>
                    ))}
                  </RadioGroup>
                ) : (
                  type.type === 'select' &&
                  (otherInputProps.disabled ? (
                    // Display plain text version
                    <div className="px-3 py-2 border rounded bg-muted text-muted-foreground text-sm">
                      {type.props.options.find(
                        (option) => option.value === field.value,
                      )?.label || 'N/A'}
                    </div>
                  ) : (
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={type.props.placeholder} />
                      </SelectTrigger>
                      <SelectContent>
                        {type.props.options.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                        {type.props.withNone && (
                          <SelectItem value={null as any}>None</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  ))
                )
              ) : (
                <Input {...field} {...otherInputProps} />
              )}
            </FormControl>
            <FormMessage />
          </div>
        </FormItem>
      )}
    />
  );
}

export default Field;
