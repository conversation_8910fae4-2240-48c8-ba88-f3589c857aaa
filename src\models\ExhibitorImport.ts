// Updated interfaces for exhibitor import functionality - 3 Phase Process

// =====================================================
// PHASE 1: UPLOAD AND VALIDATION
// =====================================================

export interface ExhibitorImportUploadDto {
  excelFile: File;
  showId: number;
}

export interface ExhibitorImportValidationResponseDto {
  sessionId: string;
  showId: number;
  fileName: string;
  summary: ExhibitorImportSummaryDto;
  rows: ExhibitorImportRowDto[];
  validationMessages: ExhibitorImportValidationMessageDto[];
  duplicates: ExhibitorImportDuplicateDto[];
  canProceed: boolean;
  expiresAt: string; // DateTime
}

export interface ExhibitorImportSummaryDto {
  totalRows: number;
  validRows: number;
  errorRows: number;
  warningRows: number;
  unresolvedDuplicates: number;
  hasErrors: boolean;
  hasWarnings: boolean;
  hasDuplicates: boolean;
}

export interface ExhibitorImportRowDto {
  rowNumber: number;
  status: string; // "Valid", "Error", "Warning"

  // Company Data (Required fields marked)
  companyName: string; // Required
  companyEmail: string; // Required
  companyCountry: string; // Required
  companyPhone?: string;
  companyAddress1?: string;
  companyAddress2?: string;
  companyCity?: string;
  companyProvince?: string;
  companyPostalCode?: string;
  companyWebsite?: string;

  // Contact Data (Required fields marked)
  contactFirstName: string; // Required
  contactLastName: string; // Required
  contactEmail: string; // Required
  boothNumbers: string; // Required
  contactPhone?: string;
  contactMobile?: string;
  contactExt?: string;

  // Validation flags
  hasErrors: boolean;
  hasWarnings: boolean;
  errorCount: number;
  warningCount: number;
}

export interface ExhibitorImportValidationMessageDto {
  rowNumber: number;
  fieldName: string;
  fieldValue: string;
  messageType: string; // "Error", "Warning"
  validationRule: string;
  messageCode: string;
  message: string;
}

export interface ExhibitorImportDuplicateDto {
  duplicateId: number;
  duplicateType: string; // "DatabaseConflict", "EmailDuplicate"
  duplicateValue: string;
  rowNumbers: number[];
  conflictDescription: string;
  requiresUserDecision: boolean;
  existingRecordId?: number;
  existingRecordType?: string;
  fieldConflicts: ExhibitorImportFieldConflictDto[];
}

export interface ExhibitorImportFieldConflictDto {
  fieldName: string;
  excelValue: string;
  databaseValue: string;
  hasConflict: boolean;
}

// =====================================================
// PHASE 2: RESOLVE DUPLICATES
// =====================================================

export interface ExhibitorImportResolveDto {
  sessionId: string;
  duplicateResolutions: ExhibitorImportDuplicateResolutionDto[];
}

export interface ExhibitorImportDuplicateResolutionDto {
  duplicateId: number;
  fieldResolutions: ExhibitorImportFieldResolutionDto[];
}

export interface ExhibitorImportFieldResolutionDto {
  fieldName: string;
  selectedSource: string; // "Excel", "Database", "Custom"
  selectedValue: string;
  excelValue: string;
  databaseValue: string;
  customValue?: string;
}

// =====================================================
// PHASE 3: EXECUTE IMPORT
// =====================================================
export interface ExhibitorImportExecuteDto {
  sessionId: string;
  sendEmailInvites: boolean; // Default: false
}

export interface ExhibitorImportExecutionResponseDto {
  sessionId: string;
  status: string; // "Executing", "Completed", "Failed"
  summary: ExhibitorImportExecutionSummaryDto;
  results: ExhibitorImportExecutionResultDto[];
  completedAt?: string; // DateTime
}

export interface ExhibitorImportExecutionSummaryDto {
  totalRows: number;
  processedRows: number;
  successfulRows: number;
  failedRows: number;
  companiesCreated: number;
  companiesUpdated: number;
  contactsCreated: number;
  contactsUpdated: number;
  exhibitorsCreated: number;
  usersCreated: number;
  emailsSent: number;
}

export interface ExhibitorImportExecutionResultDto {
  rowNumber: number;
  status: string; // "Success", "Failed"
  companyName: string;
  contactName: string;
  contactEmail: string;
  createdCompanyId?: number;
  createdContactId?: number;
  createdExhibitorId?: number;
  createdUserId?: number;
  errorMessage?: string;
  processedAt: string; // DateTime
}
