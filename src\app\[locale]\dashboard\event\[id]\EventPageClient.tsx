'use client';
import { But<PERSON> } from '@/components/ui/button';
import ShowQuery from '@/services/queries/ShowQuery';
import Notices from './components/Notices';
import WorkorderLocations from './components/WorkorderLocations';
import ShowDetails from './components/ShowDetails';
import Documents from './components/Documents';
import Schedule from './components/Schedule';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';
import { ShowSchedule, ShowPromoter } from '@/models/Show';
import { ShowLocationInList } from '@/models/ShowLocation';
import { ShowGeneralInfoData } from '@/app/[locale]/dashboard/setup/list-of-shows/components/show-tabs/GeneralInfoTab';
import PromoterInfo from './components/PromoterInfo';
import ShowStaffUsers from './components/ShowStaffUsers';
import { isSectionVisible } from '@/config/roleGroupViews';
import AuthQuery from '@/services/queries/AuthQuery';
import { useViewAsStore } from '@/stores/viewAsStore';
import { ViewAsSelector } from '@/components/ui/ViewAsSelector';

export default function EventPageClient({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const showId = Number(params.id);

  // Get current user to determine role group
  const { data: currentUser } = useQuery({
    queryKey: [AuthQuery.tags.me],
    queryFn: AuthQuery.me,
  });

  // Get view as store for role group override
  const { getEffectiveRoleGroup } = useViewAsStore();

  const { data: show, isLoading: isLoadingShow } = useQuery<
    ShowGeneralInfoData,
    Error
  >({
    queryKey: [ShowQuery.tags, showId],
    queryFn: () => ShowQuery.getOne(showId),
  });

  const { data: schedules, isLoading: isLoadingSchedules } = useQuery<
    ShowSchedule[],
    Error
  >({
    queryKey: [ShowQuery.tags, 'schedules', showId],
    queryFn: () => ShowQuery.getSchedules(showId),
    enabled: !!showId,
  });

  const { data: promoter, isLoading: isLoadingPromoter } = useQuery<
    ShowPromoter,
    Error
  >({
    queryKey: [ShowQuery.tags, 'promoter', showId],
    queryFn: () => ShowQuery.getPromoter(showId),
    enabled: !!showId,
  });

  const { data: locations, isLoading: isLoadingLocations } = useQuery<
    ShowLocationInList[],
    Error
  >({
    queryKey: [ShowLocationQuery.tags],
    queryFn: () => ShowLocationQuery.getAll(),
  });

  const isLoading =
    isLoadingShow ||
    isLoadingLocations ||
    isLoadingSchedules ||
    isLoadingPromoter;

  const locationName = locations?.find(
    (location) => location.id == Number(show?.locationId),
  )?.name;

  // Get user's role group name for conditional rendering (with view as override)
  const actualRoleGroupName = (currentUser?.role as any)?.group?.name;
  const userRoleGroupName = getEffectiveRoleGroup(actualRoleGroupName);
  console.log('userRoleGroupName', userRoleGroupName);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00646C] mx-auto mb-4"></div>
          <p className="text-slate-600">Loading event details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto pb-8 px-4">
      {/* View As Selector for event pages */}
      <div className="mb-4">
        <ViewAsSelector />
      </div>

      <div className="mb-6 flex items-center justify-between">
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.back()}
            iconName="BackIcon"
            iconProps={{ size: 20 }}
          >
            Back to Events
          </Button>
          <Button
            className="bg-[#00646C] hover:bg-[#00646C]/90 text-white"
            onClick={() =>
              router.push(`/dashboard/event/${params.id}/products`)
            }
          >
            Order Online
          </Button>
        </div>
      </div>

      {/* <EventInformation show={show!} locationName={locationName} /> */}

      <div className="flex flex-col gap-6">
        {/* Responsive grid layout that adapts to visible sections */}
        {(() => {
          // Collect all visible sections
          const visibleSections = [];

          // Top row sections (can be in 2-column layout)
          if (isSectionVisible('promoterInfo', userRoleGroupName)) {
            visibleSections.push(
              <PromoterInfo key="promoter" promoter={promoter} />,
            );
          }
          if (isSectionVisible('notices', userRoleGroupName) && show) {
            visibleSections.push(<Notices key="notices" show={show} />);
          }

          // Render top row sections with responsive grid and proper alignment
          const topRowSections = visibleSections.length > 0 && (
            <div
              className={`grid gap-6 w-full items-start ${
                visibleSections.length === 1
                  ? 'grid-cols-1'
                  : 'grid-cols-1 lg:grid-cols-2'
              }`}
            >
              {visibleSections}
            </div>
          );

          // Full-width sections
          const fullWidthSections = [];

          // Show Staff Users section (only for System Supports, Goodkey Employee, Show Manager)
          if (
            (userRoleGroupName === 'System Supports' ||
              userRoleGroupName === 'Goodkey Employee' ||
              userRoleGroupName === 'Show Manager') &&
            promoter?.showId
          ) {
            if (isSectionVisible('schedule', userRoleGroupName)) {
              fullWidthSections.push(
                <Schedule key="schedule" schedules={schedules} />,
              );
            }
            fullWidthSections.push(
              <ShowStaffUsers key="showStaff" showId={promoter.showId} />,
            );
          }

          if (isSectionVisible('workOrders', userRoleGroupName)) {
            fullWidthSections.push(
              <WorkorderLocations key="workOrders" show={show} />,
            );
          }

          if (isSectionVisible('documents', userRoleGroupName)) {
            fullWidthSections.push(
              <Documents key="documents" showId={showId} />,
            );
          }

          if (isSectionVisible('showDetails', userRoleGroupName)) {
            fullWidthSections.push(
              <ShowDetails key="showDetails" show={show} showId={showId} />,
            );
          }

          return (
            <>
              {topRowSections}
              {fullWidthSections.map((section, index) => (
                <div key={index}>{section}</div>
              ))}
            </>
          );
        })()}
      </div>
    </div>
  );
}
