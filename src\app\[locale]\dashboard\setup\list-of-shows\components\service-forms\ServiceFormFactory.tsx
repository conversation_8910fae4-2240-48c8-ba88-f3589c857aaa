import { ServiceFormType } from '@/models/Service';
import {
  JanitorialServicesForm,
  LabourInstallationDismantleForm,
  OnSiteMaterialHandlingForm,
  PorterServiceForm,
  MandatoryStorageServiceForm,
  ForkliftServiceForm,
} from './index';
import {
  JanitorialServicesFormType,
  LabourInstallationDismantleFormType,
  OnSiteMaterialHandlingFormType,
  PorterServiceFormType,
  MandatoryStorageServiceFormType,
  ForkliftServiceFormType,
} from '@/schema/ServiceFormSchemas';

interface ServiceFormFactoryProps {
  serviceType: ServiceFormType;
  onSubmit: (data: any) => void;
  initialData?: any;
  isLoading?: boolean;
}

export function ServiceFormFactory({
  serviceType,
  onSubmit,
  initialData,
  isLoading = false,
}: ServiceFormFactoryProps) {
  switch (serviceType) {
    case ServiceFormType.JANITORIAL_SERVICES:
      return (
        <JanitorialServicesForm
          onSubmit={onSubmit as (data: JanitorialServicesFormType) => void}
          initialData={initialData}
          isLoading={isLoading}
        />
      );

    case ServiceFormType.LABOUR_INSTALLATION_DISMANTLE:
      return (
        <LabourInstallationDismantleForm
          onSubmit={
            onSubmit as (data: LabourInstallationDismantleFormType) => void
          }
          initialData={initialData}
          isLoading={isLoading}
        />
      );

    case ServiceFormType.ON_SITE_MATERIAL_HANDLING:
      return (
        <OnSiteMaterialHandlingForm
          onSubmit={onSubmit as (data: OnSiteMaterialHandlingFormType) => void}
          initialData={initialData}
          isLoading={isLoading}
        />
      );

    case ServiceFormType.PORTER_SERVICE:
      return (
        <PorterServiceForm
          onSubmit={onSubmit as (data: PorterServiceFormType) => void}
          initialData={initialData}
          isLoading={isLoading}
        />
      );

    case ServiceFormType.MANDATORY_STORAGE_SERVICE:
      return (
        <MandatoryStorageServiceForm
          onSubmit={onSubmit as (data: MandatoryStorageServiceFormType) => void}
          initialData={initialData}
          isLoading={isLoading}
        />
      );

    case ServiceFormType.FORKLIFT_SERVICE:
      return (
        <ForkliftServiceForm
          onSubmit={onSubmit as (data: ForkliftServiceFormType) => void}
          initialData={initialData}
          isLoading={isLoading}
        />
      );

    default:
      return (
        <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <p className="text-gray-600">
            Form for service type "{serviceType}" is not yet implemented.
          </p>
        </div>
      );
  }
}

// Helper function to get the service form title
export function getServiceFormTitle(serviceType: ServiceFormType): string {
  switch (serviceType) {
    case ServiceFormType.JANITORIAL_SERVICES:
      return 'Janitorial Services';
    case ServiceFormType.LABOUR_INSTALLATION_DISMANTLE:
      return 'Labour (Installation and Dismantle)';
    case ServiceFormType.LABOUR_BY_TIME_RANGE:
      return 'Labour (Installation and Dismantle - by time range)';
    case ServiceFormType.ON_SITE_MATERIAL_HANDLING:
      return 'On-Site Material Handling';
    case ServiceFormType.PORTER_SERVICE:
      return 'Porter Service';
    case ServiceFormType.MANDATORY_STORAGE_SERVICE:
      return 'Mandatory Storage Service';
    case ServiceFormType.FORKLIFT_SERVICE:
      return 'Forklift (for Installation and Removal)';
    case ServiceFormType.FORKLIFT_BY_TIME_RANGE:
      return 'Forklift (for Installation and Removal - by time range)';
    case ServiceFormType.CART_DOLLY_MATERIAL_HANDLING:
      return 'Cart/Dolly Material Handling Service';
    case ServiceFormType.PRE_POST_SHOW_SERVICES:
      return 'Pre & Post Show Services';
    case ServiceFormType.ADVANCE_MATERIAL_HANDLING:
      return 'Advance Material Handling';
    case ServiceFormType.LOCAL_CARTAGE:
      return 'Local Cartage';
    case ServiceFormType.CUSTOM_BROKERAGE:
      return 'Custom Brokerage';
    case ServiceFormType.GROUND_TRANSPORTATION:
      return 'Ground Transportation';
    case ServiceFormType.POST_SHOW_STORAGE:
      return 'Post Show Storage';
    default:
      return 'Unknown Service';
  }
}
