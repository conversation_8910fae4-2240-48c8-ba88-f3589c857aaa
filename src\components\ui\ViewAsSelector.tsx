'use client';

import { useQuery } from '@tanstack/react-query';
import AuthQuery from '@/services/queries/AuthQuery';
import { useViewAsStore, VIEWABLE_ROLE_GROUPS } from '@/stores/viewAsStore';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/Badge';
import { Eye, EyeOff } from 'lucide-react';

export function ViewAsSelector() {
  const { data: currentUser } = useQuery({
    queryKey: [AuthQuery.tags.me],
    queryFn: AuthQuery.me,
  });

  const {
    viewAsRoleGroup,
    setViewAsRoleGroup,
    clearViewAs,
    isViewingAs,
    canUseViewAs,
    getEffectiveRoleGroup,
  } = useViewAsStore();

  const actualRoleGroup = (currentUser?.role as any)?.group?.name;
  const effectiveRoleGroup = getEffectiveRoleGroup(actualRoleGroup);

  // Don't show if user doesn't have permission
  if (!canUseViewAs(actualRoleGroup)) {
    return null;
  }

  return (
    <div className="inline-flex items-center gap-3 px-3 py-2 bg-blue-50 border border-blue-200 rounded-md">
      <div className="flex items-center gap-2">
        <Eye className="h-4 w-4 text-blue-600" />
        <span className="text-sm font-medium text-blue-900">View As:</span>
      </div>

      <div className="flex items-center gap-2">
        <Select
          value={viewAsRoleGroup || actualRoleGroup || ''}
          onValueChange={(value) => {
            if (value === actualRoleGroup) {
              clearViewAs();
            } else {
              setViewAsRoleGroup(value);
            }
          }}
        >
          <SelectTrigger className="w-full h-8 text-sm border-blue-300 bg-white">
            <SelectValue placeholder="Select role" />
          </SelectTrigger>
          <SelectContent>
            {VIEWABLE_ROLE_GROUPS.map((roleGroup) => (
              <SelectItem key={roleGroup} value={roleGroup}>
                <div className="flex items-center gap-2">
                  <span>{roleGroup}</span>
                  {roleGroup === actualRoleGroup && (
                    <Badge variant="outline" className="text-xs">
                      Your Role
                    </Badge>
                  )}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {isViewingAs() && (
          <Button
            variant="outline"
            size="sm"
            onClick={clearViewAs}
            className="h-8 px-2 text-xs border-blue-300 text-blue-700 hover:bg-blue-100"
          >
            <EyeOff className="h-3 w-3 mr-1" />
            Reset
          </Button>
        )}
      </div>
    </div>
  );
}
