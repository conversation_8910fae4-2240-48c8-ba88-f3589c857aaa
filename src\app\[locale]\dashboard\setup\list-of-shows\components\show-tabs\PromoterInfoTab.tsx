'use client';

import React from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';

import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Spinner } from '@/components/ui/spinner';
import ShowQuery from '@/services/queries/ShowQuery';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { ShowPromoter, Tax } from '@/models/Show';
import { AvailableTax } from '@/models/availableTax';
import { CompanyInList } from '@/models/Company';
import { ContactInList } from '@/models/Contact';

interface PromoterInfoTabProps {
  showId?: number;
  onSuccess?: () => void;
}

const PromoterInfoSchema = z.object({
  companyId: z.string().min(1, 'Company is required'),
  billedToContactIds: z.array(z.string()).min(1, 'Billing Contact is required'),
  managerContactIds: z.array(z.string()).optional(),
  showSubcontact: z.boolean().optional().default(false),
  floorPlanRequired: z.boolean().optional().default(false),
  selectedTaxes: z.record(z.boolean().optional()).optional().default({}),
});

function FormContent({
  showId,
  onSuccess,
  promoterData,
  availableTaxes,
  companies,
}: {
  showId?: number;
  onSuccess?: () => void;
  promoterData?: ShowPromoter;
  availableTaxes?: AvailableTax[];
  companies?: CompanyInList[];
}) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const isEditMode = !!showId;

  // Initialize form with useForm
  const form = useForm<z.infer<typeof PromoterInfoSchema>>({
    resolver: zodResolver(PromoterInfoSchema),
    mode: 'onChange',
    defaultValues: {
      companyId: promoterData?.companyId
        ? promoterData.companyId.toString()
        : '',
      billedToContactIds: promoterData?.billedToContactIds?.map(String) || [],
      managerContactIds: promoterData?.managerContactIds?.map(String) || [],
      showSubcontact: promoterData?.showSubcontact ?? false,
      floorPlanRequired: promoterData?.floorPlanRequired ?? false,
      selectedTaxes: (() => {
        const taxes: { [key: string]: boolean } = {};

        availableTaxes?.forEach((tax) => {
          taxes[tax.id.toString()] = false;
        });

        promoterData?.taxes?.forEach((tax: Tax) => {
          taxes[tax.taxId.toString()] = true;
        });

        return taxes;
      })(),
    },
  });

  // --- Mutation for Updating ---
  const { mutate, isPending } = useMutation({
    mutationFn: async (data: z.infer<typeof PromoterInfoSchema>) => {
      const selectedTaxes: { [key: number]: boolean } = {};
      if (data.selectedTaxes) {
        Object.entries(data.selectedTaxes).forEach(([key, value]) => {
          selectedTaxes[Number(key)] = Boolean(value);
        });
      }

      const formattedData = {
        companyId: Number(data.companyId),
        billedToContactIds: data.billedToContactIds.map(Number),
        managerContactIds: data.managerContactIds?.map(Number) || [],
        showSubcontact: data.showSubcontact || false,
        floorPlanRequired: data.floorPlanRequired || false,
        selectedTaxes,
      };

      await ShowQuery.setPromoter(showId!)(formattedData);
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        variant: 'success',
        description: 'Promoter information updated successfully',
      });
      queryClient.invalidateQueries({
        queryKey: ['Shows', showId, 'promoter'],
      });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update promoter information',
        variant: 'destructive',
      });
    },
  });

  // --- Derived Data for Form ---

  const companyOptions =
    companies
      ?.filter((company) => company.companyGroup === 'Show manager')
      .map((company) => ({
        label: company.name,
        value: company.id.toString(),
      })) || [];

  const selectedCompanyId = form.watch('companyId');

  const { data: contacts, isLoading: _isLoadingContacts } = useQuery<
    ContactInList[]
  >({
    queryKey: ['Company', selectedCompanyId, 'contacts'],
    queryFn: () => CompanyQuery.contacts.getAll(Number(selectedCompanyId)),
    enabled: !!selectedCompanyId,
  });

  const contactOptions =
    contacts?.map((contact) => ({
      label: contact.fullName || `${contact.firstName} ${contact.lastName}`,
      value: contact.id.toString(),
    })) || [];

  // --- Rendering ---
  if (!isEditMode) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Promoter Information
          </h3>
          <p className="text-gray-500">
            Promoter information can only be set in edit mode. Please save the
            general information first.
          </p>
        </div>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => mutate(data))}>
        <div className="space-y-6">
          <div>
            <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
              Promoter Information
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Field
                  control={form.control}
                  name="companyId"
                  label="Company"
                  type={{
                    type: 'select',
                    props: {
                      options: companyOptions,
                      placeholder: 'Select Company',
                    },
                  }}
                  required
                />
              </div>

              <div>
                <Field
                  control={form.control}
                  name="billedToContactIds"
                  label="Billing Contact"
                  type={{
                    type: 'multiBadgeSelect',
                    props: {
                      options: contactOptions,
                      placeholder: 'Select Billing Contacts',
                    },
                  }}
                  disabled={!selectedCompanyId}
                  required
                />
                {!selectedCompanyId && (
                  <p className="text-sm text-muted-foreground mt-1">
                    Please select a company first to load contacts.
                  </p>
                )}
              </div>
              <div>
                <Field
                  control={form.control}
                  name="managerContactIds"
                  label="Manager's contacts "
                  type={{
                    type: 'multiBadgeSelect',
                    props: {
                      options: contactOptions,
                      placeholder: 'Select Manager Contacts',
                    },
                  }}
                  disabled={!selectedCompanyId}
                />
                {!selectedCompanyId && (
                  <p className="text-sm text-muted-foreground mt-1">
                    Please select a company first to load contacts.
                  </p>
                )}
              </div>
            </div>

            <div className="mt-4 space-y-2">
              <Field
                control={form.control}
                name="showSubcontact"
                label="Show Subcontact"
                type="checkbox"
              />
              <Field
                control={form.control}
                name="floorPlanRequired"
                label="Floor Plan Required"
                type="checkbox"
              />
            </div>

            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-700">
                Selected Taxes
              </h4>
              <div className="mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                {availableTaxes?.map((tax) => (
                  <label
                    key={tax.id}
                    className="flex items-center rounded-md border p-2 text-sm text-gray-700 hover:bg-gray-50 cursor-pointer"
                  >
                    <Field
                      control={form.control}
                      name={`selectedTaxes.${tax.id}`}
                      type="checkbox"
                    />
                    <span className="ml-2">{`${tax.taxTypeName} (${tax.taxRate}%)`}</span>
                  </label>
                ))}
              </div>
            </div>

            <div className="mt-6 text-right">
              <Button
                type="submit"
                disabled={isPending}
                iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
                iconProps={{ className: 'text-white' }}
              >
                Save Changes
              </Button>
            </div>
          </div>
        </div>
      </form>
    </Form>
  );
}

const PromoterInfoTab: React.FC<PromoterInfoTabProps> = ({
  showId,
  onSuccess,
}) => {
  const { data: promoterData, isLoading: isLoadingPromoter } = useQuery({
    queryKey: ['Shows', showId, 'promoter'],
    queryFn: () => ShowQuery.getPromoter(showId!),
    enabled: !!showId,
  });

  const { data: availableTaxes, isLoading: isLoadingAvailableTaxes } = useQuery(
    {
      queryKey: ['AvailableTaxes'],
      queryFn: () => ShowQuery.getAvailableTaxes(showId!),
    },
  );

  const { data: companies, isLoading: isLoadingCompanies } = useQuery({
    queryKey: ['Companies'],
    queryFn: () => CompanyQuery.getAll(),
  });

  const isLoading =
    isLoadingPromoter || isLoadingAvailableTaxes || isLoadingCompanies;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-48">
        <Spinner size="large" />
      </div>
    );
  }

  return (
    <FormContent
      showId={showId}
      onSuccess={onSuccess}
      promoterData={promoterData}
      availableTaxes={availableTaxes}
      companies={companies}
    />
  );
};

export default PromoterInfoTab;
