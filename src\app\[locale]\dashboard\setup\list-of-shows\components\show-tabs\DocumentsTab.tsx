'use client';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import DocumentQuery from '@/services/queries/DocumentQuery';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns } from '@/lib/tableUtils';
import { useMemo, useState } from 'react';
import { Document, ShowDocumentType } from '@/models/Document';
import { ColumnDef } from '@tanstack/react-table';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import DocumentModal from './DocumentModal';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FilterIcon } from '@/assets/Icons';
import { Switch } from '@/components/ui/switch';
import { useDebounce } from '@/hooks/use-debounce';
import DatePickerWithRange from '@/components/ui/inputs/date_range_picker_input/date-range-picker-input';
import fetcher from '@/services/queries/fetcher';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DateRange } from 'react-day-picker';
// import { DocumentStatistics } from '@/models/Document'; // Removed as stats section is deleted
// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'; // Removed as stats section is deleted
import { cn } from '@/lib/utils';

interface DocumentsTabProps {
  showId?: number;
  onSuccess?: () => void;
}

export default function DocumentsTab({ showId, onSuccess }: DocumentsTabProps) {
  const queryClient = useQueryClient();

  const { data: documentTypes, isLoading: loadingDocumentTypes } = useQuery<
    ShowDocumentType[]
  >({
    queryKey: ['documents', 'available-types'],
    queryFn: DocumentQuery.getAvailableDocumentTypes,
  });

  // Removed as stats section is deleted
  // const { data: documentStats, isLoading: loadingDocumentStats } = useQuery({
  //   queryKey: ['documents', 'stats', { showId }],
  //   queryFn: () =>
  //     showId ? DocumentQuery.getShowDocumentStats(showId) : undefined,
  //   enabled: !!showId,
  // });

  // Filter state
  const [showFilters, setShowFilters] = useState(false);
  const [filterResetKey, setFilterResetKey] = useState(0);
  const [filterState, setFilterState] = useState<Record<string, any>>({});
  const debouncedFilterState = useDebounce(filterState, 500);

  const { data: documents, isLoading: loadingDocuments } = useQuery({
    queryKey: ['documents', { showId, filters: debouncedFilterState }],
    queryFn: () =>
      showId
        ? DocumentQuery.getShowDocuments(showId, {
            documentTypeId: debouncedFilterState.documentTypeId
              ? Number(debouncedFilterState.documentTypeId)
              : undefined,
            status: debouncedFilterState.status,
            isRequired: debouncedFilterState.isRequired,
            isPublic: debouncedFilterState.isPublic,
            isArchived: debouncedFilterState.isArchived,
            uploadedAfter: debouncedFilterState.uploadedAfter,
            uploadedBefore: debouncedFilterState.uploadedBefore,
            searchTerm: debouncedFilterState.searchTerm,
          })
        : DocumentQuery.getDocuments(),
    enabled: !!showId,
  });

  const handleDownload = async (id: number, name: string) => {
    try {
      // Assuming the download endpoint is /ShowDocuments/{id}/download
      const blob = (await fetcher(
        `ShowDocuments/${id}/download`,
        {},
        true,
      )) as Blob;
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = name;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (e: any) {
      toast({
        title: e.message || 'Failed to download document',
        variant: 'destructive',
      });
    }
  };

  const handleAddDocument = () => {
    modal(<DocumentModal showId={showId} />, DEFAULT_MODAL).open();
  };

  const handleEditDocument = (document: Document) => {
    modal(
      <DocumentModal docId={document.id} showId={showId} />,
      DEFAULT_MODAL,
    ).open();
  };

  const columns = useMemo<ColumnDef<Document>[]>(() => {
    return generateTableColumns<Document>(
      {
        name: { name: 'Name', type: 'text', sortable: true },
        documentTypeName: { name: 'Document Type', type: 'text' },
        originalFileName: { name: 'File Name', type: 'text' },
        // fileSize: { name: 'File Size (bytes)', type: 'text' },
        uploadedAt: {
          name: 'Uploaded At',
          type: {
            type: 'date',
            format: 'MM/dd/yyyy HH:mm',
          },
        },
        uploadedByName: { name: 'Uploaded By', type: 'text' },
        isArchived: {
          name: 'Archived',
          type: {
            type: 'node',
            render: ({ cell }) => (
              <div className="flex items-center whitespace-nowrap">
                <div
                  className={cn(
                    'w-2 h-2 rounded-full mr-1 flex-shrink-0',
                    cell ? 'bg-red-500' : 'bg-green-500',
                  )}
                />
                <span
                  className={cn(
                    'text-xs font-medium px-2 py-0.5 rounded-full inline-block whitespace-nowrap',
                    cell
                      ? 'text-red-700 bg-red-50 border border-red-200'
                      : 'text-green-700 bg-green-50 border border-green-200',
                  )}
                >
                  {cell ? 'Archived' : 'Active'}
                </span>
              </div>
            ),
          },
        },
      },
      {
        actions: {
          name: 'Actions',
          type: {
            type: 'node',
            render: ({ row }) => (
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="secondary"
                  iconName="EditIcon"
                  onClick={() => handleEditDocument(row)}
                ></Button>
                <Button
                  variant="remove"
                  size="sm"
                  iconName="RemoveIcon"
                  onClick={() => {
                    modal(
                      ({ close }) => (
                        <MutationConfirmModal
                          close={close}
                          title="Delete Document"
                          description="Are you sure you want to delete this document? This action cannot be undone."
                          mutateFn={async () => {
                            await fetcher(`ShowDocuments/${row.id}`, {
                              method: 'DELETE',
                            });
                          }}
                          mutationKey={['documents', { showId }]}
                          onSuccess={() => {
                            queryClient.invalidateQueries({
                              queryKey: ['documents', { showId }],
                            });
                            toast({
                              title: 'Document deleted',
                              variant: 'success',
                            });
                          }}
                          onError={(e: any) =>
                            toast({
                              title: e.message || 'Failed to delete document',
                              variant: 'destructive',
                            })
                          }
                          variant="destructive"
                          confirmButtonText="Delete"
                          loadingIconName="LoadingIcon"
                        />
                      ),
                      DEFAULT_MODAL,
                    ).open();
                  }}
                ></Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => handleDownload(row.id, row.originalFileName)}
                  iconName="DownloadIcon"
                ></Button>
                <Button
                  size="sm"
                  variant="secondary"
                  iconName={row.isArchived ? 'RestoreIcon' : 'ArchiveIcon'}
                  onClick={() => {
                    const isArchived = row.isArchived;
                    const title = isArchived
                      ? 'Unarchive Document'
                      : 'Archive Document';
                    const description = isArchived
                      ? 'Are you sure you want to unarchive this document?'
                      : 'Are you sure you want to archive this document?';
                    const mutateFn = async () =>
                      DocumentQuery.archiveDocument(
                        row.id,
                        isArchived ? 'Unarchived by user' : 'Archived by user',
                      );
                    const confirmButtonText = isArchived
                      ? 'Unarchive'
                      : 'Archive';

                    const toastTitle = isArchived
                      ? 'Document unarchived'
                      : 'Document archived';
                    const toastMessage = isArchived
                      ? 'Failed to unarchive document'
                      : 'Failed to archive document';

                    modal(
                      ({ close }) => (
                        <MutationConfirmModal
                          close={close}
                          title={title}
                          description={description}
                          mutateFn={mutateFn}
                          mutationKey={['documents', { showId }]}
                          onSuccess={() => {
                            queryClient.invalidateQueries({
                              queryKey: ['documents', { showId }],
                            });
                            toast({
                              title: toastTitle,
                              variant: 'success',
                            });
                          }}
                          onError={(e: any) =>
                            toast({
                              title: e.message || toastMessage,
                              variant: 'destructive',
                            })
                          }
                          confirmButtonText={confirmButtonText}
                          loadingIconName="LoadingIcon"
                        />
                      ),
                      DEFAULT_MODAL,
                    ).open();
                  }}
                ></Button>
              </div>
            ),
          },
        },
      },
      false,
    );
  }, [showId, documentTypes]);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
          Documents
        </h2>

        <div className="flex flex-row justify-end gap-4 mb-4 ">
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => setShowFilters(!showFilters)}
          >
            <FilterIcon className="h-4 w-4" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </Button>
          <Button
            variant={'primary'}
            iconName="AddIcon"
            onClick={handleAddDocument}
          >
            Add Document
          </Button>
        </div>

        {showFilters && (
          <div
            key={filterResetKey}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6 p-4 border border-slate-200 rounded-md shadow-sm"
          >
            <div className="flex flex-col gap-2">
              <Label htmlFor="search-term">Search</Label>
              <Input
                id="search-term"
                placeholder="Name, description, or filename"
                value={filterState.searchTerm || ''}
                onChange={(e) =>
                  setFilterState((prev) => ({
                    ...prev,
                    searchTerm: e.target.value,
                  }))
                }
              />
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="document-type">Document Type</Label>
              <Select
                value={filterState.documentTypeId || ''}
                onValueChange={(value) =>
                  setFilterState((prev) => ({
                    ...prev,
                    documentTypeId: value,
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select document type" />
                </SelectTrigger>
                <SelectContent>
                  {(documentTypes || []).map((type) => (
                    <SelectItem key={type.id} value={String(type.id)}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-2">
              <Label>Uploaded Date Range</Label>
              <DatePickerWithRange
                onValueChange={(range: DateRange | undefined) => {
                  setFilterState((prev) => ({
                    ...prev,
                    uploadedAfter: range?.from?.toISOString(),
                    uploadedBefore: range?.to?.toISOString(),
                  }));
                }}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="is-required"
                checked={filterState.isRequired || false}
                onCheckedChange={(checked) =>
                  setFilterState((prev) => ({ ...prev, isRequired: checked }))
                }
              />
              <Label htmlFor="is-required">Is Required</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="is-public"
                checked={filterState.isPublic || false}
                onCheckedChange={(checked) =>
                  setFilterState((prev) => ({ ...prev, isPublic: checked }))
                }
              />
              <Label htmlFor="is-public">Is Public</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="is-archived"
                checked={filterState.isArchived || false}
                onCheckedChange={(checked) =>
                  setFilterState((prev) => ({ ...prev, isArchived: checked }))
                }
              />
              <Label htmlFor="is-archived">Is Archived</Label>
            </div>
            <div className="col-span-full flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setFilterState({});
                  setFilterResetKey((prev) => prev + 1);
                }}
              >
                Clear Filters
              </Button>
            </div>
          </div>
        )}

        {loadingDocuments ||
        loadingDocumentTypes /* Removed loadingDocumentStats */ ? (
          <div className="min-h-[400px] flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading documents...</p>
            </div>
          </div>
        ) : (
          <DataTable
            columns={columns}
            data={documents || []}
            isLoading={loadingDocuments}
          />
        )}
      </div>
    </div>
  );
}
