import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';

// Define HallContact interface based on the actual API response from ShowQuery.getHallContact
interface HallContact {
  showId: number;
  hallId: number;
  contactId: number;
  hallName: string;
  hallCode: string;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
}

export default function HallContactInfo({
  hallContact,
}: {
  hallContact?: HallContact;
}) {
  if (!hallContact) {
    return null; // Or a loading/placeholder state
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Hall Contact Information</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div>
            <span className="font-medium text-slate-700">Hall Name:</span>
            <span className="ml-2">{hallContact.hallName}</span>
          </div>
          <div>
            <span className="font-medium text-slate-700">Hall Code:</span>
            <span className="ml-2">{hallContact.hallCode}</span>
          </div>
          <div>
            <span className="font-medium text-slate-700">Contact Name:</span>
            <span className="ml-2">{hallContact.contactName}</span>
          </div>
          <div>
            <span className="font-medium text-slate-700">Contact Email:</span>
            <span className="ml-2">{hallContact.contactEmail}</span>
          </div>
          <div>
            <span className="font-medium text-slate-700">Contact Phone:</span>
            <span className="ml-2">{hallContact.contactPhone}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
