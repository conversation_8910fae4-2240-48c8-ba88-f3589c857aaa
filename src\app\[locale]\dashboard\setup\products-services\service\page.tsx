import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import ServiceTableAccordion from './components/service_table_accordion';
import GroupTypeQuery from '@/services/queries/GroupTypeQuery';

export const metadata: Metadata = {
  title: 'Goodkey | Service',
};

export default async function ShowFacilityPage() {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery({
    queryKey: ['Services', { groupType: 2 }],
    queryFn: () => GroupTypeQuery.getAllByGroupIdHierarchical(2),
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        {
          title: 'Products & Services',
          link: '/dashboard/setup',
        },
        {
          title: 'Service',
          link: '/dashboard/setup/products-services/service',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <ServiceTableAccordion />
      </HydrationBoundary>
    </AppLayout>
  );
}
