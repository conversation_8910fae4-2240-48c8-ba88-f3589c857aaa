'use client';

import { useQuery } from '@tanstack/react-query';
import { FaPlus } from 'react-icons/fa';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import ShowDocQuery from '@/services/queries/ShowDocQuery';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import { ShowDoc } from '@/models/ShowDoc';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import ShowDocModal from '../show_doc_modal';

interface ShowDocTableProps {
  locationId: number;
}

export const ShowDocTable = ({ locationId }: ShowDocTableProps) => {
  const { data, isLoading } = useQuery({
    queryKey: ['ShowDoc', { locationId }],
    queryFn: () => ShowDocQuery.getByLocation(locationId),
    enabled: !!locationId,
  });

  const columns = generateTableColumns<ShowDoc>(
    {
      id: { name: 'ID', type: 'text' },
      docType: { name: 'Type', type: 'text' },
      originalFilename: { name: 'Filename', type: 'text' },
      validUntil: { name: 'Valid Until', type: 'text' },
      location: { name: 'Location', type: 'text' },
      hall: { name: 'Hall', type: 'text' },
      createdBy: { name: 'Created By', type: 'text' },
      updatedBy: { name: 'Updated By', type: 'text' },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <Button
              size="sm"
              variant="secondary"
              iconName="EditIcon"
              onClick={() => {
                modal(<ShowDocModal docId={row.id} locationId={locationId} />, {
                  ...DEFAULT_MODAL,
                  width: 'w-full',
                }).open();
              }}
            ></Button>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<ShowDoc>({
    docType: { name: 'Document Type', type: 'text' },
    originalFilename: { name: 'Filename', type: 'text' },
    createdBy: { name: 'Created By', type: 'text' },
    updatedBy: { name: 'Updated By', type: 'text' },
  });

  return (
    <DataTable
      columns={columns}
      filterFields={filters}
      data={data}
      isLoading={isLoading}
      controls={
        <Button
          variant="main"
          onClick={() => {
            modal(<ShowDocModal locationId={locationId} />, {
              ...DEFAULT_MODAL,
              width: 'w-full',
            }).open();
          }}
        >
          <FaPlus />
          Add New Document
        </Button>
      }
    />
  );
};

export default ShowDocTable;
