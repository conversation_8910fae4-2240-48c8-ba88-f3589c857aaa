import fetcher from './fetcher';
import type {
  ExhibitorImportValidationResponseDto,
  ExhibitorImportResolveDto,
  ExhibitorImportExecuteDto,
  ExhibitorImportExecutionResponseDto,
} from '@/models/ExhibitorImport';
import type {
  ExhibitorImportFieldEditRequestDto,
  ExhibitorImportFieldEditResponseDto,
  ExhibitorImportRowComparisonRequestDto,
  ExhibitorImportRowComparisonResponseDto,
} from '@/models/ExhibitorImportFix';

const ExhibitorImportQuery = {
  tags: ['ExhibitorImport'] as const,

  // =====================================================
  // PHASE 1: UPLOAD AND VALIDATION
  // =====================================================
  upload: async (
    file: File,
    showId: number,
  ): Promise<ExhibitorImportValidationResponseDto> => {
    const formData = new FormData();
    formData.append('excelFile', file);
    formData.append('showId', showId.toString());

    return fetcher<ExhibitorImportValidationResponseDto>(
      'ExhibitorImport/upload',
      {
        method: 'POST',
        body: formData,
      },
      true,
    );
  },

  // =====================================================
  // PHASE 2: RESOLVE DUPLICATES
  // =====================================================
  resolve: async (
    request: ExhibitorImportResolveDto,
  ): Promise<{ data: string; statusCode: number; message: string }> => {
    return fetcher<{ data: string; statusCode: number; message: string }>(
      'ExhibitorImport/resolve',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      },
    );
  },

  // =====================================================
  // PHASE 2.5: FIELD EDITING AND ERROR FIXING
  // =====================================================

  /**
   * Edit individual field values to fix validation errors
   */
  editFields: async (
    request: ExhibitorImportFieldEditRequestDto,
  ): Promise<ExhibitorImportFieldEditResponseDto> => {
    return fetcher<ExhibitorImportFieldEditResponseDto>(
      'ExhibitorImport/edit-fields',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      },
    );
  },

  /**
   * Compare a row with existing database records for conflict resolution
   */
  compareRow: async (
    request: ExhibitorImportRowComparisonRequestDto,
  ): Promise<ExhibitorImportRowComparisonResponseDto> => {
    return fetcher<ExhibitorImportRowComparisonResponseDto>(
      'ExhibitorImport/compare-row',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      },
    );
  },

  // =====================================================
  // PHASE 3: EXECUTE IMPORT
  // =====================================================
  execute: async (
    request: ExhibitorImportExecuteDto,
  ): Promise<ExhibitorImportExecutionResponseDto> => {
    return fetcher<ExhibitorImportExecutionResponseDto>(
      'ExhibitorImport/execute',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      },
    );
  },
};

export default ExhibitorImportQuery;
