import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ShowGeneralInfoData } from '@/app/[locale]/dashboard/setup/list-of-shows/components/show-tabs/GeneralInfoTab';
import { useQuery } from '@tanstack/react-query';
import ShowHallQuery from '@/services/queries/ShowHallQuery';
import ShowQuery from '@/services/queries/ShowQuery';
import { Hall } from '@/models/ShowLocation';
import LabelValueRow from '@/components/ui/label_value_row/label-value-row';

export default function ShowDetails({
  show,
  showId,
}: {
  show?: ShowGeneralInfoData;
  showId: number;
}) {
  const {
    data: showHallContact,
    isLoading: isLoadingShowHallContact,
    isError: isErrorShowHallContact,
  } = useQuery({
    queryKey: [ShowQuery.tags, showId, 'hallContact'],
    queryFn: () => ShowQuery.getHallContact(showId),
    enabled: !!showId,
  });

  const {
    data: hallData,
    isLoading: isLoadingHallData,
    isError: isErrorHallData,
  } = useQuery<Hall>({
    queryKey: [ShowHallQuery.tags, showHallContact?.hallId, 'hallDetail'],
    queryFn: () => ShowHallQuery.get(showHallContact!.hallId!),
    enabled: !!showHallContact?.hallId,
  });

  const isLoading = isLoadingShowHallContact || isLoadingHallData;
  const isError = isErrorShowHallContact || isErrorHallData;

  console.log('hallData', hallData);

  if (!show || isLoading) {
    return (
      <Card>
        <CardHeader className="pb-5">
          <CardTitle>Hall Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-4">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00646C] mx-auto mb-4"></div>
              <p className="text-slate-600">Loading hall info...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-5">
        <CardTitle>Hall Information</CardTitle>
      </CardHeader>
      <CardContent>
        {hallData ? (
          <div className="flex flex-col gap-6">
            <p className="text-lg font-semibold">
              {hallData.hallName} ({hallData.hallCode})
            </p>
            <div className="grid grid-cols-3 gap-6 text-sm">
              <LabelValueRow
                title="Hall Style"
                value={hallData.hallStyle || '-'}
              />
              <LabelValueRow
                title="Floor Type"
                value={hallData.hallFloorType || '-'}
              />
              <LabelValueRow
                title="Banquet Capacity"
                value={hallData.banquetCapacity?.toString() || '-'}
              />
              <LabelValueRow
                title="Width"
                value={hallData.hallWidth?.toString() || '-'}
              />
              <LabelValueRow
                title="Length"
                value={hallData.hallLength?.toString() || '-'}
              />
              <LabelValueRow
                title="Overhead Height"
                value={hallData.overheadHeight?.toString() || '-'}
              />
              <LabelValueRow
                title="Area"
                value={hallData.hallArea?.toString() || '-'}
              />
              <LabelValueRow
                title="Elec On Floor"
                value={hallData.isElecOnFloor ? 'Yes' : 'No'}
              />
              <LabelValueRow
                title="Elec On Ceiling"
                value={hallData.isElecOnCeiling ? 'Yes' : 'No'}
              />
              <LabelValueRow
                title="Hall Surface"
                value={hallData.hallSurface || '-'}
              />
              <LabelValueRow
                title="Ceiling Height"
                value={hallData.hallCeilingHeight?.toString() || '-'}
              />
              <LabelValueRow
                title="Access Door"
                value={hallData.accessDoor?.toString() || '-'}
              />
              <LabelValueRow
                title="Loading Docks"
                value={hallData.loadingDocks?.toString() || '-'}
              />
              <LabelValueRow
                title="Booth Count"
                value={hallData.hallBoothCount?.toString() || '-'}
              />
            </div>
          </div>
        ) : (
          <p className="text-center text-gray-500">
            No hall information available for this show.
          </p>
        )}
      </CardContent>
    </Card>
  );
}
