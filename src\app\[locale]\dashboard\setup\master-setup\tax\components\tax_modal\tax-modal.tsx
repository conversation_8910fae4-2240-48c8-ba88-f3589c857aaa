'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Spinner } from '@/components/ui/spinner';
import { useToast } from '@/components/ui/use-toast';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { modal } from '@/components/ui/overlay';
import Suspense from '@/components/ui/Suspense';
import { getQueryClient } from '@/utils/query-client';
import TaxQuery from '@/services/queries/TaxQuery';
import { TaxRequestData, TaxRequestSchema } from '@/schema/Tax';
import Field from '@/components/ui/inputs/field';
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';

function FormContent({
  defaultValues,
  id,
}: {
  defaultValues?: TaxRequestData;
  id?: number;
}) {
  const { toast } = useToast();
  const form = useForm<TaxRequestData>({
    resolver: zodResolver(TaxRequestSchema),
    defaultValues: {
      provinceId: defaultValues?.provinceId.toString(),
      taxTypeId: defaultValues?.taxTypeId.toString(),
      displayOrder: defaultValues?.displayOrder?.toString() ?? '0',
      taxRate: defaultValues?.taxRate?.toString() ?? '0',
    },
  });

  const {
    data: provinces,
    isLoading: isLoadingProvinces,
    isSuccess: isSuccessProvinces,
  } = useQuery({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
  });

  const {
    data: taxTypes,
    isLoading: isLoadingTaxTypes,
    isSuccess: isSuccessTaxTypes,
  } = useQuery({
    queryKey: ['Tax Types'],
    queryFn: TaxQuery.getTaxType,
  });
  const { mutate, isPending } = useMutation({
    mutationFn: id ? TaxQuery.update(id) : TaxQuery.create,
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({ queryKey: TaxQuery.tags });
      if (id) {
        await getQueryClient().invalidateQueries({
          queryKey: ['Tax Info', { id }],
        });
      }
      toast({
        title: 'Success',
        description: id ? 'Tax updated successfully' : 'New tax created',
        variant: 'success',
      });
      modal.close();
    },
  });

  return (
    <Form {...form}>
      <ModalContainer
        title={id ? 'Update Tax' : 'Add Tax'}
        description={id ? 'Update tax details' : 'Create new tax entry'}
        onSubmit={form.handleSubmit((data) => mutate(data))}
        controls={
          <div className="flex justify-end items-center gap-4">
            <Button
              variant={'main'}
              disabled={isPending}
              iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
              iconProps={{ className: isPending ? 'animate-spin' : '' }}
            >
              {isPending ? 'Saving...' : 'Save'}
            </Button>
          </div>
        }
      >
        <div className="flex flex-col gap-2 mt-4">
          {isLoadingProvinces && !isSuccessProvinces ? (
            <Spinner />
          ) : (
            <Field
              control={form.control}
              name="provinceId"
              label="Province"
              required
              disabled={id ? true : false}
              type={{
                type: 'select',
                props: {
                  options:
                    provinces?.map((province) => ({
                      label: province.name,
                      value: province.id.toString(),
                    })) ?? [],
                  placeholder: 'Select Province',
                },
              }}
            />
          )}

          {!isLoadingTaxTypes && isSuccessTaxTypes ? (
            <Field
              control={form.control}
              name="taxTypeId"
              label="Tax Type"
              required
              disabled={id ? true : false}
              type={{
                type: 'select',
                props: {
                  options:
                    taxTypes?.map((taxType) => ({
                      label: taxType.name,
                      value: taxType.id.toString(),
                    })) ?? [],
                  placeholder: 'Select Tax Type',
                },
              }}
            />
          ) : (
            <Spinner />
          )}
          <Field
            control={form.control}
            name="taxRate"
            label="Tax Rate (%)"
            type="text"
          />
          <Field
            control={form.control}
            name="displayOrder"
            label="Display Order"
            type="text"
          />
        </div>
      </ModalContainer>
    </Form>
  );
}

interface TaxModalProps {
  id?: number;
}

function TaxModal({ id }: TaxModalProps) {
  const {
    data: tax,
    isPaused,
    isLoading,
  } = useQuery({
    queryKey: ['Tax Info', { id }],
    queryFn: () => TaxQuery.getTax(Number(id!)),
    enabled: !!id,
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {isLoading && isPaused ? (
        <Spinner />
      ) : (
        <FormContent defaultValues={tax} id={tax ? Number(id) : undefined} />
      )}
    </Suspense>
  );
}

export default TaxModal;
