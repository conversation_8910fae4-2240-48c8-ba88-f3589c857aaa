import {
  ShowIn<PERSON><PERSON>,
  ShowCreateRequest,
  Show<PERSON>romoter,
  ShowUpdateRequest,
  ShowScheduleCreateRequest,
  ShowScheduleUpdateRequest,
  ShowSchedule,
  SetPromoterRequest,
} from '@/models/Show';
import fetcher from './fetcher';
import { ShowGeneralInfoData } from '@/app/[locale]/dashboard/setup/list-of-shows/components/show-tabs/GeneralInfoTab';
import { AvailableTax } from '@/models/availableTax';
import { ContactDetail } from '@/models/Contact';

// Show Staff Users interfaces
export interface ShowStaffUser {
  id: number;
  showId: number;
  userId: number;
  role: string;
  isActive: boolean;
  userName: string;
  userEmail: string;
  userPhone: string;
  createdAt: string;
  createdBy?: number;
}

export interface AddShowStaffUserRequest {
  userId: number;
}

export interface CreateUserRequest {
  firstName: string;
  lastName: string;
  verificationEmail: string;
  mobileNumber?: string;
  // Fixed values for temporary show staff
  statusId: number; // Always 1 (Active)
  salutationId: number; // Always 1 (Default)
  departmentId: number; // Always 1 (Default)
  roleId: number; // Always 11 (Show Staff)
}

export interface CreateShowStaffUserRequest {
  firstName: string;
  lastName: string;
  verificationEmail: string;
  mobileNumber?: string;
}

// Show Logo interfaces
export interface ShowLogo {
  id: number;
  showId: number;
  fileName: string;
  originalFileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  uploadedAt: string;
  uploadedByUsername?: string;
  updatedAt: string;
  updatedByUsername?: string;
}

export interface UpdateUserRequest {
  firstName: string;
  lastName: string;
  verificationEmail: string;
  mobileNumber?: string;
}

const ShowQuery = {
  tags: ['Shows'] as const,

  getAll: async () => fetcher<ShowInList[]>('Shows'),

  getOne: async (id: number) =>
    fetcher<ShowGeneralInfoData>(`Shows/${id}/general-info`),

  create: async (data: ShowCreateRequest) =>
    fetcher<number>('Shows/general-info', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  update: (id: number) => async (data: ShowUpdateRequest) =>
    fetcher<boolean>(`Shows/${id}/general-info`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  delete: async (id: number) =>
    fetcher<boolean>(`Shows/${id}/general-info`, {
      method: 'DELETE',
    }),

  toggleArchive: async (id: number) =>
    fetcher<boolean>(`Shows/${id}/toggle-archive`, {
      method: 'PATCH',
    }),

  getHallContact: async (id: number) =>
    fetcher<{
      showId: number;
      hallId: number;
      contactId: number;
      hallName: string;
      hallCode: string;
      contactName: string;
      contactEmail: string;
      contactPhone: string;
    }>(`Shows/${id}/hall`),

  updateHallContact:
    (id: number) => async (data: { hallId: number; contactId: number }) =>
      fetcher<boolean>(`Shows/${id}/hall`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      }),

  // --- Show Schedules ---

  getSchedules: async (showId: number) =>
    fetcher<ShowSchedule[]>(`/shows/${showId}/schedules`),
  getSchedule: async (id: number) =>
    fetcher<ShowSchedule>(`/shows/schedules/${id}`),

  createSchedule: async (showId: number, data: ShowScheduleCreateRequest) =>
    fetcher(`/shows/${showId}/schedules`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  updateSchedule:
    (showId: number, id: number) => async (data: ShowScheduleUpdateRequest) =>
      fetcher(`/shows/${showId}/schedules/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      }),

  deleteSchedule: async (showId: number, id: number) =>
    fetcher(`/shows/${showId}/schedules/${id}`, { method: 'DELETE' }),

  toggleScheduleConfirmed: async (showId: number, id: number) =>
    fetcher(`/shows/${showId}/schedules/${id}/toggle-confirmed`, {
      method: 'PATCH',
    }),

  toggleScheduleApplyToService: async (showId: number, id: number) =>
    fetcher(`/shows/${showId}/schedules/${id}/toggle-apply-to-service`, {
      method: 'PATCH',
    }),

  // --- Show Promoter ---
  getPromoter: async (showId: number) =>
    fetcher<ShowPromoter>(`/Shows/${showId}/promoter`),

  getAvailableTaxes: async (showId: number) =>
    fetcher<AvailableTax[]>(`/Shows/${showId}/available-taxes`),

  setPromoter: (showId: number) => async (data: SetPromoterRequest) => {
    return fetcher<boolean>(`/Shows/${showId}/promoter`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
  },

  getShowContacts: async (showId: number) =>
    fetcher<{
      showId: number;
      showName: string;
      showCode: string;
      companyName: string;
      billedToContacts: ContactDetail[];
      managerContacts: ContactDetail[];
    }>(`/Shows/${showId}/contacts`),

  // Show Staff Users methods
  getStaffUsers: async (showId: number) =>
    fetcher<ShowStaffUser[]>(`Shows/${showId}/staff`),

  addStaffUser: async (showId: number, data: AddShowStaffUserRequest) =>
    fetcher<ShowStaffUser>(`Shows/${showId}/staff`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  removeStaffUser: async (showId: number, userId: number) =>
    fetcher<boolean>(`Shows/${showId}/staff/${userId}`, {
      method: 'DELETE',
    }),

  toggleStaffUserStatus: async (showId: number, userId: number) =>
    fetcher<boolean>(`Shows/${showId}/staff/${userId}/toggle-active`, {
      method: 'PATCH',
    }),

  // Update staff user information
  updateStaffUser: async (
    showId: number,
    userId: number,
    data: UpdateUserRequest,
  ) =>
    fetcher<ShowStaffUser>(`Shows/${showId}/staff/${userId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  // Create user and add to show staff
  createUserAndAddToShow: async (
    showId: number,
    userData: CreateShowStaffUserRequest,
  ) =>
    fetcher<ShowStaffUser>(`Shows/${showId}/staff`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData),
    }),

  // Logo methods
  getShowLogo: async (showId: number) =>
    fetcher<ShowLogo>(`Shows/${showId}/logo`),

  setShowLogo: async (showId: number, file: File) => {
    const formData = new FormData();
    formData.append('File', file);
    return fetcher<ShowLogo>(
      `Shows/${showId}/logo`,
      {
        method: 'POST',
        body: formData,
      },
      true,
    );
  },

  deleteShowLogo: async (showId: number) =>
    fetcher<boolean>(`Shows/${showId}/logo`, {
      method: 'DELETE',
    }),
};

export default ShowQuery;
