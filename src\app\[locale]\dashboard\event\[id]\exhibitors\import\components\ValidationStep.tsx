'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/Badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  ChevronRight,
  Eye,
  EyeOff,
} from 'lucide-react';
import type { ExhibitorImportValidationResponseDto } from '@/models/ExhibitorImport';

interface ValidationStepProps {
  validationData: ExhibitorImportValidationResponseDto;
  onProceed: () => void;
  isLoading: boolean;
}

const ValidationStep: React.FC<ValidationStepProps> = ({
  validationData,
  onProceed,
  isLoading,
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [selectedTab, setSelectedTab] = useState('summary');

  const { summary, rows, validationMessages, duplicates, canProceed } =
    validationData;

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'valid':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getMessageTypeColor = (messageType: string) => {
    switch (messageType.toLowerCase()) {
      case 'error':
        return 'text-red-600';
      case 'warning':
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  const errorRows = rows.filter((row) => row.hasErrors);
  const warningRows = rows.filter((row) => row.hasWarnings && !row.hasErrors);
  const validRows = rows.filter((row) => !row.hasErrors && !row.hasWarnings);

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold">Validation Results</h2>
        <p className="text-muted-foreground">
          Review the validation results and resolve any issues before
          proceeding.
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {summary.totalRows}
            </div>
            <div className="text-sm text-muted-foreground">Total Rows</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {summary.validRows}
            </div>
            <div className="text-sm text-muted-foreground">Valid Rows</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">
              {summary.errorRows}
            </div>
            <div className="text-sm text-muted-foreground">Error Rows</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {summary.warningRows}
            </div>
            <div className="text-sm text-muted-foreground">Warning Rows</div>
          </CardContent>
        </Card>
      </div>

      {/* Status Alert */}
      {summary.hasErrors ? (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            There are {summary.errorRows} rows with errors that must be fixed
            before importing.
            {summary.hasDuplicates &&
              ` Additionally, there are ${summary.unresolvedDuplicates} duplicate conflicts that need resolution.`}
          </AlertDescription>
        </Alert>
      ) : summary.hasDuplicates ? (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Data validation passed, but there are {summary.unresolvedDuplicates}{' '}
            duplicate conflicts that need resolution before importing.
          </AlertDescription>
        </Alert>
      ) : (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            All data validation passed successfully! Ready to proceed with
            import.
          </AlertDescription>
        </Alert>
      )}

      {/* Details Toggle */}
      <div className="flex justify-center">
        <Button
          variant="outline"
          onClick={() => setShowDetails(!showDetails)}
          className="flex items-center space-x-2"
        >
          {showDetails ? (
            <EyeOff className="h-4 w-4" />
          ) : (
            <Eye className="h-4 w-4" />
          )}
          <span>{showDetails ? 'Hide Details' : 'Show Details'}</span>
        </Button>
      </div>

      {/* Detailed Results */}
      {showDetails && (
        <Card>
          <CardHeader>
            <CardTitle>Detailed Results</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={selectedTab} onValueChange={setSelectedTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="errors">
                  Errors ({summary.errorRows})
                </TabsTrigger>
                <TabsTrigger value="warnings">
                  Warnings ({summary.warningRows})
                </TabsTrigger>
                <TabsTrigger value="duplicates">
                  Duplicates ({duplicates.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="summary" className="space-y-4">
                <div className="grid gap-4">
                  {validRows.length > 0 && (
                    <div className="p-4 border rounded-lg bg-green-50">
                      <h4 className="font-medium text-green-800 mb-2">
                        Valid Rows ({validRows.length})
                      </h4>
                      <p className="text-sm text-green-700">
                        These rows passed all validation checks and are ready
                        for import.
                      </p>
                    </div>
                  )}

                  {warningRows.length > 0 && (
                    <div className="p-4 border rounded-lg bg-yellow-50">
                      <h4 className="font-medium text-yellow-800 mb-2">
                        Warning Rows ({warningRows.length})
                      </h4>
                      <p className="text-sm text-yellow-700">
                        These rows have warnings but can still be imported.
                        Review the warnings in the Warnings tab.
                      </p>
                    </div>
                  )}

                  {errorRows.length > 0 && (
                    <div className="p-4 border rounded-lg bg-red-50">
                      <h4 className="font-medium text-red-800 mb-2">
                        Error Rows ({errorRows.length})
                      </h4>
                      <p className="text-sm text-red-700">
                        These rows have errors that must be fixed before import.
                        Review the errors in the Errors tab.
                      </p>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="errors" className="space-y-4">
                {errorRows.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-600" />
                    <p>No errors found!</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {errorRows.slice(0, 10).map((row) => (
                      <Card key={row.rowNumber} className="border-red-200">
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start mb-2">
                            <span className="font-medium">
                              Row {row.rowNumber}
                            </span>
                            <Badge className={getStatusColor(row.status)}>
                              {row.status}
                            </Badge>
                          </div>
                          <div className="text-sm space-y-1">
                            <p>
                              <strong>Company:</strong> {row.companyName}
                            </p>
                            <p>
                              <strong>Contact:</strong> {row.contactFirstName}{' '}
                              {row.contactLastName}
                            </p>
                            <p>
                              <strong>Email:</strong> {row.contactEmail}
                            </p>
                          </div>
                          <div className="mt-2 space-y-1">
                            {validationMessages
                              .filter(
                                (msg) =>
                                  msg.rowNumber === row.rowNumber &&
                                  msg.messageType === 'Error',
                              )
                              .map((msg, idx) => (
                                <div
                                  key={idx}
                                  className="text-sm text-red-600 flex items-start space-x-2"
                                >
                                  <AlertCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                                  <span>{msg.message}</span>
                                </div>
                              ))}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                    {errorRows.length > 10 && (
                      <p className="text-center text-muted-foreground">
                        ... and {errorRows.length - 10} more error rows
                      </p>
                    )}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="warnings" className="space-y-4">
                {warningRows.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-600" />
                    <p>No warnings found!</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {warningRows.slice(0, 10).map((row) => (
                      <Card key={row.rowNumber} className="border-yellow-200">
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start mb-2">
                            <span className="font-medium">
                              Row {row.rowNumber}
                            </span>
                            <Badge className={getStatusColor(row.status)}>
                              {row.status}
                            </Badge>
                          </div>
                          <div className="text-sm space-y-1">
                            <p>
                              <strong>Company:</strong> {row.companyName}
                            </p>
                            <p>
                              <strong>Contact:</strong> {row.contactFirstName}{' '}
                              {row.contactLastName}
                            </p>
                            <p>
                              <strong>Email:</strong> {row.contactEmail}
                            </p>
                          </div>
                          <div className="mt-2 space-y-1">
                            {validationMessages
                              .filter(
                                (msg) =>
                                  msg.rowNumber === row.rowNumber &&
                                  msg.messageType === 'Warning',
                              )
                              .map((msg, idx) => (
                                <div
                                  key={idx}
                                  className="text-sm text-yellow-600 flex items-start space-x-2"
                                >
                                  <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                                  <span>{msg.message}</span>
                                </div>
                              ))}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                    {warningRows.length > 10 && (
                      <p className="text-center text-muted-foreground">
                        ... and {warningRows.length - 10} more warning rows
                      </p>
                    )}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="duplicates" className="space-y-4">
                {duplicates.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-600" />
                    <p>No duplicates found!</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {duplicates.map((duplicate) => (
                      <Card
                        key={duplicate.duplicateId}
                        className="border-blue-200"
                      >
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start mb-2">
                            <span className="font-medium">
                              {duplicate.duplicateType}
                            </span>
                            <Badge variant="outline">
                              {duplicate.duplicateValue}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            {duplicate.conflictDescription}
                          </p>
                          <p className="text-sm">
                            <strong>Affected Rows:</strong>{' '}
                            {duplicate.rowNumbers.join(', ')}
                          </p>
                          {duplicate.requiresUserDecision && (
                            <div className="mt-2 text-sm text-blue-600">
                              <AlertCircle className="h-3 w-3 inline mr-1" />
                              Requires manual resolution
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* Next Step Info */}
      {summary.errorRows > 0 && (
        <Card className="bg-blue-50 border-blue-200 mb-4">
          <CardContent className="p-4 text-center">
            <div className="text-blue-800">
              <strong>Next Step:</strong> You'll be able to edit field values
              and fix validation errors in the next step.
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4">
        <Button
          onClick={onProceed}
          disabled={isLoading}
          size="lg"
          className="min-w-[200px]"
        >
          {summary.errorRows > 0 || summary.warningRows > 0 ? (
            <>
              Fix Issues & Review Data
              <ChevronRight className="h-4 w-4 ml-2" />
            </>
          ) : duplicates.length > 0 ? (
            <>
              Resolve Duplicates
              <ChevronRight className="h-4 w-4 ml-2" />
            </>
          ) : (
            <>
              Review Data
              <ChevronRight className="h-4 w-4 ml-2" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default ValidationStep;
