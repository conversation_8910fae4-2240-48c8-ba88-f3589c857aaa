'use client';

import { useQuery } from '@tanstack/react-query';
import GroupTypeQuery from '@/services/queries/GroupTypeQuery';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Spinner } from '@/components/ui/spinner';
import CategoryDisplay from '../category_display';

export const ProductTableAccordion = () => {
  const groupId = 1;
  const { data, isLoading } = useQuery({
    queryKey: GroupTypeQuery.tags,
    queryFn: () => GroupTypeQuery.getAllByGroupIdHierarchical(groupId),
  });

  if (isLoading)
    return (
      <div>
        <Spinner />
      </div>
    );

  return (
    <div className="w-full">
      <div className="w-full mx-auto">
        <div className="pl-4">
          {/* Header for columns */}
          <div className="grid grid-cols-[1fr_80px_100px_80px] gap-2 mb-2 pb-2 border-b border-gray-200">
            <div className="text-slate-600 font-medium">Name</div>
            <div className="text-slate-600 font-medium text-left -ml-[200px]">
              Code
            </div>
            <div className="text-slate-600 font-medium text-left -ml-[70px]">
              Actions
            </div>
            <div className="text-slate-600 font-medium">Status</div>
          </div>
          {data &&
            data.group.length > 0 &&
            data.group.map((g) => (
              <Accordion
                key={g.groupId}
                type="single"
                collapsible
                className="space-y-3"
              >
                <AccordionItem value={g.groupId.toString()}>
                  <AccordionTrigger
                    className={`pl-3 py-1 hover:no-underline hover:bg-slate-50 hover:rounded-lg`}
                  >
                    <div className="grid grid-cols-[1fr_80px_100px_80px] gap-2 items-center py-1 hover:bg-gray-50 w-full">
                      <div className="flex items-center gap-1.5 cursor-pointer hover:text-main hover:underline">
                        <span
                          className={`text-md font-medium ${g.categories.length === 0 ? 'text-gray-500' : 'text-gray-900'} truncate flex items-center gap-1 hover:text-main`}
                        >
                          {g.groupName}
                        </span>
                        {/* {g.categories.length === 0 && (
                      <span className="text-xs text-gray-600 line-through">
                        No category available
                      </span>
                    )} */}
                      </div>
                      <div className="text-left -ml-[200px]">
                        <span
                          className="font-mono font-normal text-sm"
                          style={{ color: '#B10055' }}
                        >
                          {g.code}
                        </span>
                      </div>
                      <div></div> {/* Empty actions column for groups */}
                      <div className="-ml-[40px]">
                        {g.isAvailable ? (
                          <span className="text-[rgba(0,91,99,1)] text-sm font-normal">
                            Active
                          </span>
                        ) : (
                          <span className="text-red-600 text-sm font-normal">
                            Discontinued
                          </span>
                        )}
                      </div>{' '}
                      {/* Empty status column for groups */}
                      {/* <Link
                    href={`/dashboard/setup/product/${groupId}/category/${category.categoryId}/add`}
                  >
                    <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                      <FaPlus className="h-3 w-3" />
                    </Button>
                  </Link> */}
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="pl-4 pb-3 w-full">
                    {g.categories && g.categories.length > 0 ? (
                      g.categories.map((c) => (
                        <CategoryDisplay
                          key={c.categoryId}
                          data={c}
                          groupId={groupId}
                          categoryId={c.categoryId}
                        />
                      ))
                    ) : (
                      <div className="text-sm text-gray-500 italic">
                        No category in this group.
                      </div>
                    )}
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            ))}
        </div>
      </div>
    </div>
  );
};

export default ProductTableAccordion;
