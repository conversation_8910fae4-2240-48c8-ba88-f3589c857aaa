import type { Metadata } from 'next';
import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import ShowExhibitorsQuery from '@/services/queries/ShowExhibitorsQuery';
import ShowQuery from '@/services/queries/ShowQuery';
import CompanyQuery from '@/services/queries/CompanyQuery';
import ContactTypeQuery from '@/services/queries/ContactTypeQuery';
import ExhibitorPageClient from './components/ExhibitorPageClient';

export const metadata: Metadata = {
  title: 'Exhibitor | GOODKEY SHOW SERVICES LTD.',
  description:
    'Add or edit exhibitor information at GOODKEY SHOW SERVICES LTD.',
};

export default async function ExhibitorPage({
  params,
}: {
  params: Promise<{ id: string; exhibitorId: string }>;
}) {
  try {
    const { id: showId, exhibitorId } = await params;
    const isAdd = exhibitorId === 'add';
    const numericShowId = Number(showId);
    const numericExhibitorId = isAdd ? undefined : Number(exhibitorId);

    if (Number.isNaN(numericShowId)) {
      throw new Error('Invalid show ID');
    }

    if (!isAdd && Number.isNaN(numericExhibitorId)) {
      throw new Error('Invalid exhibitor ID');
    }

    const client = getQueryClient();

    // Prefetch show data
    await client.prefetchQuery({
      queryKey: [ShowQuery.tags, numericShowId],
      queryFn: () => ShowQuery.getOne(numericShowId),
    });

    // Prefetch exhibitor data if editing
    if (!isAdd && numericExhibitorId) {
      await client.prefetchQuery({
        queryKey: [ShowExhibitorsQuery.tags, numericExhibitorId],
        queryFn: () => ShowExhibitorsQuery.getById(numericExhibitorId),
      });
    }

    // Prefetch companies
    await client.prefetchQuery({
      queryKey: [CompanyQuery.tags, 'exhibitor'],
      queryFn: () => CompanyQuery.getAll('exhibitor'),
    });

    // Prefetch contact types
    await client.prefetchQuery({
      queryKey: [ContactTypeQuery.tags],
      queryFn: ContactTypeQuery.getAll,
    });

    return (
      <HydrationBoundary state={dehydrate(client)}>
        <ExhibitorPageClient
          showId={numericShowId}
          exhibitorId={numericExhibitorId}
          isAdd={isAdd}
        />
      </HydrationBoundary>
    );
  } catch (error) {
    redirect(`/dashboard/event/${params.then((p) => p.id)}/exhibitors`);
  }
}
