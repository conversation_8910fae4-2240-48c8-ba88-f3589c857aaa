import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Role groups that can use the "View As" feature
const PRIVILEGED_ROLE_GROUPS = ['System Supports', 'Goodkey Employee'];

// Available role groups to view as
export const VIEWABLE_ROLE_GROUPS = [
  'Show Manager',
  'Supplier',
  'Exhibitor',
  'Goodkey Employee',
  'System Supports',
];

interface ViewAsState {
  viewAsRoleGroup: string | null;
  setViewAsRoleGroup: (roleGroup: string | null) => void;
  clearViewAs: () => void;
  isViewingAs: () => boolean;
  canUseViewAs: (userRoleGroup?: string) => boolean;
  getEffectiveRoleGroup: (actualRoleGroup?: string) => string | undefined;
}

export const useViewAsStore = create<ViewAsState>()(
  persist(
    (set, get) => ({
      viewAsRoleGroup: null,

      setViewAsRoleGroup: (roleGroup: string | null) => {
        set({ viewAsRoleGroup: roleGroup });
      },

      clearViewAs: () => {
        set({ viewAsRoleGroup: null });
      },

      isViewingAs: () => {
        return get().viewAsRoleGroup !== null;
      },

      canUseViewAs: (userRoleGroup?: string) => {
        return userRoleGroup
          ? PRIVILEGED_ROLE_GROUPS.includes(userRoleGroup)
          : false;
      },

      getEffectiveRoleGroup: (actualRoleGroup?: string) => {
        const { viewAsRoleGroup, canUseViewAs } = get();

        // Only apply view as if user has permission
        if (viewAsRoleGroup && canUseViewAs(actualRoleGroup)) {
          return viewAsRoleGroup;
        }

        return actualRoleGroup;
      },
    }),
    {
      name: 'view-as-storage', // localStorage key
      partialize: (state) => ({ viewAsRoleGroup: state.viewAsRoleGroup }), // Only persist viewAsRoleGroup
    },
  ),
);
