'use client';

import './style/index.scss';
import Suspense from '@/components/ui/Suspense';
import { useQuery } from '@tanstack/react-query';
import { CheckCircle, XCircle } from 'lucide-react';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';
import { ShowLocationGeneralData } from '@/schema/ShowLocationSchema';
interface IFacilityInfoBox {
  id?: number;
}

function FacilityInfoBox({ id }: IFacilityInfoBox) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['ShowLocation', { id }],
    queryFn: () => ShowLocationQuery.get(id!),
    enabled: !!id,
    select: (res: ShowLocationGeneralData) =>
      ({
        locationCode: res.locationCode ?? '',
        name: res.name ?? '',
        telephone: res.telephone ?? '',
        tollfree: res.tollfree ?? '',
        fax: res.fax ?? '',
        mapLink: res.mapLink ?? '',
        website: res.website ?? '',
        email: res.email ?? '',
        accessPlan: res.accessPlan,
        accessPlanPath: res.accessPlanPath ?? '',
        isArchived: res.isArchived ?? false,
      }) as ShowLocationGeneralData,
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <div className="product-info-box">
        <div className="mb-4">
          <h1 className="text-2xl font-bold text-slate-800 mb-2 border-b border-[#00646C] pb-2">
            {data ? data.name : 'Setup a new product'}
            {data && (
              <span
                className={`inline-flex items-center gap-1 text-xs font-medium px-2 py-0.5 rounded-full`}
              >
                {!data.isArchived ? (
                  <>
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </>
                ) : (
                  <>
                    <XCircle className="w-4 h-4 text-red-600" />
                  </>
                )}
              </span>
            )}
          </h1>
          <p className="text-slate-600 text-sm">
            {data ? (
              'Edit the details below to update the facility.'
            ) : (
              <span>
                Please fill out the form below to add a new facility. Required
                fields are marked with a{' '}
                <span className="text-red-500 font-semibold">*</span>.
              </span>
            )}
          </p>
        </div>
      </div>
    </Suspense>
  );
}

export default FacilityInfoBox;
