// components/OfferingDisplayOut.tsx
'use client';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { PropertyOptionsDto } from '@/models/Offering';
import OptionRow from '../option_row';
interface OfferingDisplayProps {
  id: number;
  name?: string | null;
  groupId: number;
  categoryId: number;
  options?: PropertyOptionsDto[];
  code?: string | null;
  isActive?: boolean | null;
  warehouseId?: number | null;
  isDiscontinued?: boolean | null;
  quantity?: number | null;
  price?: number | null;
}

const OfferingDisplay: React.FC<OfferingDisplayProps> = ({
  id,
  name,
  groupId,
  categoryId,
  options,
  code,
  isActive,
  isDiscontinued,
  quantity,
  price,
}) => {
  return (
    <Accordion key={id} type="single" collapsible className="space-y-3">
      <AccordionItem value={name + id.toString()}>
        <div
          className={`pl-3 pr-1 hover:no-underline hover:bg-slate-50 hover:rounded-lg`}
        >
          <div className="grid grid-cols-[1fr_50px_50px_50px_50px] gap-2 items-center py-1 hover:bg-gray-50 w-full">
            <div>
              <AccordionTrigger className="cursor-pointer hover:text-main hover:underline flex items-center gap-1">
                <span
                  className={`text-md font-medium ${options && options.length === 0 ? 'text-gray-500' : 'text-gray-900'} truncate flex items-center gap-1 hover:text-main`}
                >
                  {name}
                </span>
              </AccordionTrigger>
            </div>
            <div className="text-left -ml-[260px]"></div>
            <div className="text-left -ml-[190px]">
              <div className="relative"></div>
            </div>
            <div className="-ml-[80px]"></div>
            <div className="-ml-[20px]"></div>
          </div>
        </div>
        <AccordionContent className="ml-4 border-l border-gray-200 pb-0">
          <div>
            {options && options.length > 0 ? (
              options.map((o, index) => (
                <div
                  key={o.id}
                  className={`pl-3 pr-1 hover:no-underline hover:bg-slate-50 hover:rounded-lg`}
                >
                  <div
                    className={`${
                      index < options.length - 1
                        ? 'border-b border-gray-200'
                        : ''
                    }`}
                  >
                    <OptionRow option={o} offeringId={id} />
                  </div>
                </div>
              ))
            ) : (
              <div className="ml-3 pl-2 text-sm text-gray-500 italic">
                No property for this product.
              </div>
            )}
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default OfferingDisplay;
