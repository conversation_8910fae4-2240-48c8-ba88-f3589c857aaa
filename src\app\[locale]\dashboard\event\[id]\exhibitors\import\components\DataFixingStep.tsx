'use client';

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/Badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  AlertCircle,
  AlertTriangle,
  Edit3,
  Users,
  Building,
  Mail,
  Phone,
  Save,
  RotateCcw,
  Eye,
  EyeOff,
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import type {
  ExhibitorImportValidationResponseDto,
  ExhibitorImportRowDto,
  ExhibitorImportValidationMessageDto,
} from '@/models/ExhibitorImport';

interface DataFixingStepProps {
  validationData: ExhibitorImportValidationResponseDto;
  onDataFixed: (fixedData: any) => void;
  isLoading: boolean;
}

interface FieldEdit {
  rowNumber: number;
  fieldName: string;
  originalValue: string;
  newValue: string;
  isModified: boolean;
}

interface DuplicateResolution {
  duplicateId: number;
  selectedAction: 'merge' | 'keep_excel' | 'keep_database' | 'custom';
  fieldResolutions: Record<string, string>;
}

const DataFixingStep: React.FC<DataFixingStepProps> = ({
  validationData,
  onDataFixed,
  isLoading,
}) => {
  const [fieldEdits, setFieldEdits] = useState<Record<string, FieldEdit>>({});
  const [duplicateResolutions, setDuplicateResolutions] = useState<
    Record<number, DuplicateResolution>
  >({});
  const [selectedTab, setSelectedTab] = useState('errors');
  const [showOnlyModified, setShowOnlyModified] = useState(false);

  const { rows, validationMessages, duplicates } = validationData;

  // Get rows with errors
  const errorRows = rows.filter((row) => row.hasErrors);
  const warningRows = rows.filter((row) => row.hasWarnings && !row.hasErrors);

  // Group validation messages by row
  const messagesByRow = validationMessages.reduce(
    (acc, msg) => {
      if (!acc[msg.rowNumber]) acc[msg.rowNumber] = [];
      acc[msg.rowNumber].push(msg);
      return acc;
    },
    {} as Record<number, ExhibitorImportValidationMessageDto[]>,
  );

  const getFieldIcon = (fieldName: string) => {
    if (fieldName.toLowerCase().includes('email'))
      return <Mail className="h-4 w-4" />;
    if (fieldName.toLowerCase().includes('phone'))
      return <Phone className="h-4 w-4" />;
    if (fieldName.toLowerCase().includes('company'))
      return <Building className="h-4 w-4" />;
    return <Users className="h-4 w-4" />;
  };

  const formatFieldName = (fieldName: string) => {
    return fieldName
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .trim();
  };

  const getFieldValue = (
    row: ExhibitorImportRowDto,
    fieldName: string,
  ): string => {
    const fieldMap: Record<string, keyof ExhibitorImportRowDto> = {
      companyName: 'companyName',
      companyEmail: 'companyEmail',
      companyPhone: 'companyPhone',
      companyAddress1: 'companyAddress1',
      companyAddress2: 'companyAddress2',
      contactFirstName: 'contactFirstName',
      contactLastName: 'contactLastName',
      contactEmail: 'contactEmail',
      contactPhone: 'contactPhone',
      contactMobile: 'contactMobile',
      boothNumbers: 'boothNumbers',
    };

    const mappedField = fieldMap[fieldName];
    return mappedField ? String(row[mappedField] || '') : '';
  };

  const updateFieldValue = (
    rowNumber: number,
    fieldName: string,
    newValue: string,
    originalValue: string,
  ) => {
    const editKey = `${rowNumber}-${fieldName}`;
    setFieldEdits((prev) => ({
      ...prev,
      [editKey]: {
        rowNumber,
        fieldName,
        originalValue,
        newValue,
        isModified: newValue !== originalValue,
      },
    }));
  };

  const resetFieldValue = (rowNumber: number, fieldName: string) => {
    const editKey = `${rowNumber}-${fieldName}`;
    setFieldEdits((prev) => {
      const newEdits = { ...prev };
      delete newEdits[editKey];
      return newEdits;
    });
  };

  const getDisplayValue = (
    row: ExhibitorImportRowDto,
    fieldName: string,
  ): string => {
    const editKey = `${row.rowNumber}-${fieldName}`;
    const edit = fieldEdits[editKey];
    return edit ? edit.newValue : getFieldValue(row, fieldName);
  };

  const isFieldModified = (rowNumber: number, fieldName: string): boolean => {
    const editKey = `${rowNumber}-${fieldName}`;
    return fieldEdits[editKey]?.isModified || false;
  };

  const getModifiedFieldsCount = (): number => {
    return Object.values(fieldEdits).filter((edit) => edit.isModified).length;
  };

  const handleSaveChanges = () => {
    const modifiedFields = Object.values(fieldEdits).filter(
      (edit) => edit.isModified,
    );

    if (modifiedFields.length === 0) {
      toast({
        title: 'No changes to save',
        description: "You haven't made any modifications to the data.",
        variant: 'default',
      });
      return;
    }

    // Here you would call an API to save the field changes
    console.log('Saving field changes:', modifiedFields);
    console.log('Duplicate resolutions:', duplicateResolutions);

    toast({
      title: 'Changes saved successfully',
      description: `Updated ${modifiedFields.length} field${modifiedFields.length > 1 ? 's' : ''}.`,
    });

    onDataFixed({
      fieldEdits: modifiedFields,
      duplicateResolutions: Object.values(duplicateResolutions),
    });
  };

  const renderFieldEditor = (
    row: ExhibitorImportRowDto,
    fieldName: string,
    messages: ExhibitorImportValidationMessageDto[],
  ) => {
    const originalValue = getFieldValue(row, fieldName);
    const currentValue = getDisplayValue(row, fieldName);
    const isModified = isFieldModified(row.rowNumber, fieldName);
    const fieldMessages = messages.filter((msg) => msg.fieldName === fieldName);

    return (
      <div
        key={fieldName}
        className={`border rounded-lg p-4 ${isModified ? 'border-blue-300 bg-blue-50' : 'border-gray-200'}`}
      >
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            {getFieldIcon(fieldName)}
            <Label className="font-medium">{formatFieldName(fieldName)}</Label>
            {isModified && (
              <Badge variant="outline" className="bg-blue-100 text-blue-800">
                Modified
              </Badge>
            )}
          </div>
          {isModified && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => resetFieldValue(row.rowNumber, fieldName)}
              className="text-gray-500 hover:text-gray-700"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Reset
            </Button>
          )}
        </div>

        <div className="space-y-2">
          <Input
            value={currentValue}
            onChange={(e) =>
              updateFieldValue(
                row.rowNumber,
                fieldName,
                e.target.value,
                originalValue,
              )
            }
            className={`${isModified ? 'border-blue-400 focus:border-blue-500' : ''}`}
            placeholder={`Enter ${formatFieldName(fieldName).toLowerCase()}`}
          />

          {fieldMessages.length > 0 && (
            <div className="space-y-1">
              {fieldMessages.map((msg, idx) => (
                <div
                  key={idx}
                  className={`text-sm flex items-start space-x-2 ${
                    msg.messageType === 'Error'
                      ? 'text-red-600'
                      : 'text-yellow-600'
                  }`}
                >
                  {msg.messageType === 'Error' ? (
                    <AlertCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                  ) : (
                    <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                  )}
                  <span>{msg.message}</span>
                </div>
              ))}
            </div>
          )}

          {isModified && (
            <div className="text-xs text-blue-600 bg-blue-100 p-2 rounded">
              <strong>Original:</strong> {originalValue || '(empty)'}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
          <Edit3 className="h-8 w-8 text-blue-600" />
        </div>
        <div>
          <h2 className="text-2xl font-semibold">Fix Data Issues</h2>
          <p className="text-muted-foreground">
            Review and fix validation errors, warnings, and duplicate conflicts
            in your data.
          </p>
        </div>
      </div>

      {/* Summary */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">
              {errorRows.length}
            </div>
            <div className="text-sm text-muted-foreground">
              Rows with Errors
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {warningRows.length}
            </div>
            <div className="text-sm text-muted-foreground">
              Rows with Warnings
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">
              {duplicates.length}
            </div>
            <div className="text-sm text-muted-foreground">
              Duplicate Conflicts
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {getModifiedFieldsCount()}
            </div>
            <div className="text-sm text-muted-foreground">Fields Modified</div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowOnlyModified(!showOnlyModified)}
            className="flex items-center space-x-2"
          >
            {showOnlyModified ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
            <span>{showOnlyModified ? 'Show All' : 'Show Only Modified'}</span>
          </Button>
        </div>

        <Button
          onClick={handleSaveChanges}
          disabled={isLoading || getModifiedFieldsCount() === 0}
          className="flex items-center space-x-2"
        >
          <Save className="h-4 w-4" />
          <span>Save Changes ({getModifiedFieldsCount()})</span>
        </Button>
      </div>
    </div>
  );
};

export default DataFixingStep;
