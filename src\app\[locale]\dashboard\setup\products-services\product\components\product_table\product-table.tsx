'use client';

import { useQuery } from '@tanstack/react-query';
import { CheckCircle, XCircle } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Link } from '@/utils/navigation';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import OfferingQuery from '@/services/queries/OfferingQuery';
import { OfferingDto } from '@/models/Offering';

export const ProductTable = () => {
  const { data, isLoading } = useQuery({
    queryKey: OfferingQuery.tags,
    queryFn: () => OfferingQuery.getAllByGroupIdFlat(2), // Use the flat version of offerings
  });

  const columns = generateTableColumns<OfferingDto>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      code: { name: 'Code', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      supplierItemNumber: {
        name: 'Supplier Item Number',
        type: 'text',
        sortable: true,
      },
      publicDescription: {
        name: 'Public Description',
        type: 'text',
        sortable: true,
      },
      isActive: {
        name: 'Active',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <CheckCircle className="text-green-600 w-4 h-4 mr-1" />
              ) : (
                <XCircle className="text-red-600 w-4 h-4 mr-1" />
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 justify-center">
              <Link href={`/dashboard/setup/product/${row.id ?? 'add'}`}>
                <Button
                  size="sm"
                  variant="secondary"
                  iconName="EditIcon"
                ></Button>
              </Link>
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<OfferingDto>({
    name: { name: 'Name', type: 'text' },
    code: { name: 'Code', type: 'text' },
    supplierItemNumber: { name: 'Supplier Item Number', type: 'text' },
    isActive: {
      name: 'Active',
      type: {
        type: 'select',
        options: [
          { label: 'Yes', value: 'true' },
          { label: 'No', value: 'false' },
        ],
      },
    },
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      filterFields={filters}
      isLoading={isLoading}
      controls={
        <div className="flex flex-row gap-2 justify-end">
          <Link href="/dashboard/setup/product/add">
            <Button variant="main">
              <FaPlus />
              Add New Product
            </Button>
          </Link>
        </div>
      }
    />
  );
};

export default ProductTable;
