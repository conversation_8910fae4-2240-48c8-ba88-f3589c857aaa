'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { ShowExhibitorFilters } from '@/models/ShowExhibitor';

interface SearchCriteria {
  searchType: 'companyName' | 'contactName' | 'boothNumber' | 'searchTerm';
  searchValue: string;
}

interface ExhibitorSearchFormProps {
  onSearch: (filters: ShowExhibitorFilters) => void;
}

export function ExhibitorSearchForm({ onSearch }: ExhibitorSearchFormProps) {
  const [searchCriteria, setSearchCriteria] = useState<SearchCriteria>({
    searchType: 'companyName',
    searchValue: '',
  });
  const [hasSearched, setHasSearched] = useState(false);

  const searchTypeOptions = [
    { value: 'companyName', label: 'Company Name' },
    { value: 'contactName', label: 'Contact Name' },
    { value: 'boothNumber', label: 'Booth Number' },
    { value: 'searchTerm', label: 'General Search' },
  ];

  const handleSearch = () => {
    if (searchCriteria.searchValue.trim() === '') {
      return;
    }
    setHasSearched(true);

    const filters: ShowExhibitorFilters = {
      isActive: true,
    };

    // Map search criteria to filter properties
    switch (searchCriteria.searchType) {
      case 'companyName':
        filters.companyName = searchCriteria.searchValue;
        break;
      case 'contactName':
        filters.contactName = searchCriteria.searchValue;
        break;
      case 'boothNumber':
        filters.boothNumber = searchCriteria.searchValue;
        break;
      case 'searchTerm':
        filters.searchTerm = searchCriteria.searchValue;
        break;
    }

    onSearch(filters);
  };

  const handleReset = () => {
    setSearchCriteria({
      searchType: 'companyName',
      searchValue: '',
    });
    setHasSearched(false);
    onSearch({
      isActive: true,
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Search Exhibitors</CardTitle>
        <CardDescription>
          Select your search criteria and enter a value to find specific
          exhibitors.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="searchType">Search By</Label>
            <Select
              value={searchCriteria.searchType}
              onValueChange={(value: any) =>
                setSearchCriteria((prev) => ({ ...prev, searchType: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select search type" />
              </SelectTrigger>
              <SelectContent>
                {searchTypeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="searchValue">Search Value</Label>
            <Input
              id="searchValue"
              placeholder={`Enter ${searchTypeOptions.find((opt) => opt.value === searchCriteria.searchType)?.label.toLowerCase()}...`}
              value={searchCriteria.searchValue}
              onChange={(e) =>
                setSearchCriteria((prev) => ({
                  ...prev,
                  searchValue: e.target.value,
                }))
              }
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch();
                }
              }}
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={handleSearch}
            disabled={searchCriteria.searchValue.trim() === ''}
            iconName="SearchIcon"
            iconProps={{ className: 'text-white' }}
          >
            Search Exhibitors
          </Button>
          <Button variant="outline" onClick={handleReset} iconName="ClearIcon">
            Clear
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
