export interface DocumentType {
  id: number;
  name: string;
  description: string;
}

export interface ShowDocumentType {
  id: number;
  name: string;
  extensionCode: string;
  extension: string;
  isAvailable: boolean;
  isImage: boolean;
}

export interface Document {
  id: number;
  showId: number;
  documentTypeId: number;
  name: string;
  description: string;
  fileName: string;
  originalFileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  isRequired: boolean;
  isPublic: boolean;
  version: number;
  status: string;
  uploadedAt: string;
  uploadedById: number;
  updatedAt: string | null;
  updatedById: number | null;
  archivedAt: string | null;
  archivedById: number | null;
  isArchived: boolean;
  showName: string;
  showCode: string;
  documentTypeName: string;
  uploadedByName: string;
  updatedByName: string | null;
  archivedByName: string | null;
  file?: File[]; // For upload purposes
}

export interface DocumentStatistics {
  totalDocuments: number;
  activeDocuments: number;
  archivedDocuments: number;
  requiredDocuments: number;
  publicDocuments: number;
  totalFileSize: number;
  documentsByType: { [key: string]: number };
  documentsByStatus: { [key: string]: number };
}
