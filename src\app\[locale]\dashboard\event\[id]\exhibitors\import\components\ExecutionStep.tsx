'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Play, CheckCircle, Mail, Loader2 } from 'lucide-react';

interface ExecutionStepProps {
  onExecute: (sendEmailInvites: boolean) => void;
  isLoading: boolean;
}

const ExecutionStep: React.FC<ExecutionStepProps> = ({
  onExecute,
  isLoading,
}) => {
  const [sendEmailInvites, setSendEmailInvites] = useState(false);

  const handleExecute = () => {
    onExecute(sendEmailInvites);
  };

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold">Ready to Import</h2>
        <p className="text-muted-foreground">
          All validation checks have passed and duplicates have been resolved.
          Configure your import settings and execute the import.
        </p>
      </div>

      {/* Success Alert */}
      <Alert className="border-green-200 bg-green-50">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800">
          Data is ready for import! All validation checks have passed and
          duplicate conflicts have been resolved.
        </AlertDescription>
      </Alert>

      {/* Import Options */}
      <Card>
        <CardHeader>
          <CardTitle>Import Options</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-start space-x-3">
            <Checkbox
              id="sendEmailInvites"
              checked={sendEmailInvites}
              onCheckedChange={(checked) =>
                setSendEmailInvites(checked as boolean)
              }
              disabled={isLoading}
            />
            <div className="space-y-1">
              <label
                htmlFor="sendEmailInvites"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
              >
                Send Email Invitations
              </label>
              <p className="text-sm text-muted-foreground">
                Create user accounts for all contacts and send email invitations
                with login credentials. Default password will be "blue".
              </p>
            </div>
          </div>

          {sendEmailInvites && (
            <Alert>
              <Mail className="h-4 w-4" />
              <AlertDescription>
                Email invitations will be sent to all contacts with valid email
                addresses. Make sure your email service is configured properly.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Execute Button */}
      <div className="flex justify-center">
        <Button
          onClick={handleExecute}
          disabled={isLoading}
          size="lg"
          className="min-w-[200px]"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Importing...
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-2" />
              Execute Import
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default ExecutionStep;
