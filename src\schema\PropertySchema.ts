import * as z from 'zod';

export const PropertyCreateSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  description: z.string().optional().nullable(),
});
export type PropertyCreateData = z.infer<typeof PropertyCreateSchema>;

export const PropertyUpdateSchema = PropertyCreateSchema.extend({
  code: z.string().optional().nullable(),
});

export type PropertyUpdateData = z.infer<typeof PropertyUpdateSchema>;

export const ServicePropertySchema = z.object({
  serviceSelections: z.array(z.string()).min(1, 'Billing Contact is required'),
});
export type ServicePropertyData = z.infer<typeof ServicePropertySchema>;
