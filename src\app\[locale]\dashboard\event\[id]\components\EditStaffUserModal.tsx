'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { modal } from '@/components/ui/overlay';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import ShowQuery, {
  ShowStaffUser,
  UpdateUserRequest,
} from '@/services/queries/ShowQuery';
import {
  UpdateStaffUserSchema,
  UpdateStaffUserFormData,
} from '@/schema/StaffUserSchema';

export default function EditStaffUserModal({
  showId,
  staffUser,
}: {
  showId: number;
  staffUser: ShowStaffUser;
}) {
  const queryClient = useQueryClient();

  const form = useForm<UpdateStaffUserFormData>({
    resolver: zodResolver(UpdateStaffUserSchema),
    defaultValues: {
      firstName: staffUser.userName.split(' ')[0] || '',
      lastName: staffUser.userName.split(' ').slice(1).join(' ') || '',
      verificationEmail: staffUser.userEmail || '',
      mobileNumber: staffUser.userPhone || '',
    },
  });

  const updateUserMutation = useMutation({
    mutationFn: (formData: UpdateStaffUserFormData) => {
      const userData: UpdateUserRequest = {
        ...formData,
      };
      return ShowQuery.updateStaffUser(showId, staffUser.userId, userData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [ShowQuery.tags, showId, 'staff'],
      });
      toast({
        title: 'Success',
        description: 'Staff user updated successfully',
        variant: 'success',
      });
      modal.close();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update staff user',
        variant: 'destructive',
      });
    },
  });

  const onSubmit = (data: UpdateStaffUserFormData) => {
    updateUserMutation.mutate(data);
  };

  return (
    <Form {...form}>
      <ModalContainer
        title="Edit Staff User"
        description="Update the information for this show staff user."
        onSubmit={form.handleSubmit(onSubmit)}
        controls={
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => modal.close()}>
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={updateUserMutation.isPending}
              iconName="SaveIcon"
              iconProps={{
                className: 'text-white',
              }}
            >
              {updateUserMutation.isPending
                ? 'Updating...'
                : 'Update Staff User'}
            </Button>
          </div>
        }
      >
        <div className="grid gap-4 py-4">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <Field
              control={form.control}
              name="firstName"
              label="First Name"
              required
              type="text"
              placeholder="Enter first name"
            />
            <Field
              control={form.control}
              name="lastName"
              label="Last Name"
              required
              type="text"
              placeholder="Enter last name"
            />
          </div>

          {/* Email */}
          <Field
            control={form.control}
            name="verificationEmail"
            label="Email"
            required
            type="email"
            placeholder="Enter email address"
          />

          {/* Phone (optional) */}
          <Field
            control={form.control}
            name="mobileNumber"
            label="Phone Number"
            type="text"
            placeholder="Enter phone number (optional)"
          />

          {/* Role info (read-only) */}
          <div className="bg-gray-50 p-3 rounded-md">
            <p className="text-sm text-gray-600">
              <strong>Role:</strong> Show Staff (temporary access for this show
              only)
            </p>
          </div>
        </div>
      </ModalContainer>
    </Form>
  );
}
