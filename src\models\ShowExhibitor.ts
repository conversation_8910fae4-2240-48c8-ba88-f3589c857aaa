// API response and request types
export interface ShowExhibitorInList {
  id: number;
  showId: number;
  companyId: number;
  contactId: number;
  boothNumber: string[];
  isActive: boolean;
  isArchived: boolean;
  createdAt: string;
  updatedAt: string;
  archivedAt: string | null;
  showName: string;
  showCode: string;
  companyName: string;
  contactName: string;
  contactEmail: string;
  contactTelephone: string;
  contactCellphone: string;
  contactExt: string;
  contactIsRegistered: boolean;
  createdByName: string;
  updatedByName: string;
  archivedByName: string | null;
}

export interface ShowExhibitorDetail {
  id: number;
  showId: number;
  companyId: number;
  contactId: number;
  boothNumber: string[];
  isActive: boolean;
  isArchived: boolean;
  createdAt: string;
  updatedAt: string;
  archivedAt: string | null;
  showName: string;
  showCode: string;
  companyName: string;
  contactFirstName: string;
  contactLastName: string;
  contactEmail: string;
  contactTelephone: string;
  contactCellphone: string;
  contactTypeId: number;
  contactExt: string;
  contactIsRegistered: boolean;
  createdByName: string;
  updatedByName: string;
  archivedByName: string | null;
}

export interface ShowExhibitorCreateRequest {
  showId: number;
  companyId: number;
  contactId?: number;
  boothNumber?: string[];
  // Contact creation fields (used when contactId is null)
  contactTypeId?: number;
  firstName?: string;
  lastName?: string;
  email?: string;
  telephone?: string;
  ext?: string;
  cellphone?: string;
  sendEmailInvite?: boolean;
}

export interface ShowExhibitorUpdateRequest {
  companyId?: number;
  contactId?: number | null;
  boothNumber?: string[];
  isActive?: boolean;
  // Contact information (creates new or updates existing)
  contactTypeId?: number;
  firstName?: string;
  lastName?: string;
  email?: string;
  telephone?: string;
  ext?: string;
  cellphone?: string;
  // Optional: Send email invitation for new contacts
  sendEmailInvite?: boolean;
}

export interface ShowExhibitorArchiveRequest {
  archiveReason?: string;
}

export interface ShowExhibitorStats {
  totalExhibitors: number;
  activeExhibitors: number;
  archivedExhibitors: number;
  exhibitorsWithBooths: number;
  exhibitorsWithoutBooths: number;
  exhibitorsByCompany: Record<string, number>;
  allBoothNumbers: string[];
}

export interface ShowExhibitorFilters {
  companyId?: number;
  companyName?: string;
  contactName?: string;
  boothNumber?: string;
  isActive?: boolean;
  isArchived?: boolean;
  searchTerm?: string;
}

// Additional types for UI components
export interface ExhibitorSearchFilters {
  searchType: 'booth' | 'company' | 'all';
  searchTerm: string;
  companyName?: string;
  contactName?: string;
  boothNumber?: string;
  isActive?: boolean;
  isArchived?: boolean;
}

export interface ExhibitorFormData {
  showId: number;
  companyId: number;
  contactId?: number;
  boothNumber: string[];
  // Contact creation fields
  contactTypeId: number;
  firstName: string;
  lastName: string;
  email: string;
  telephone: string;
  ext: string;
  cellphone: string;
  sendEmailInvite: boolean;
  // Form mode
  useExistingContact: boolean;
}

export interface BoothValidationResult {
  isValid: boolean;
  unavailableBooths: string[];
  message?: string;
}
