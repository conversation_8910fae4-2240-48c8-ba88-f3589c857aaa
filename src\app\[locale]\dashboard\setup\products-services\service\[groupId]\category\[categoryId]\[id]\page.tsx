import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import OfferingQuery from '@/services/queries/OfferingQuery';
import OfferingGeneralInfo from './component/product_general_info';

type PageParams = {
  params: Promise<{
    groupId: string;
    categoryId: string;
    id: string;
  }>;
};

export default async function Page({ params }: PageParams) {
  const client = getQueryClient();

  const { groupId, categoryId, id } = await params;

  // Check if it's not the "add" page
  if (id !== 'add') {
    const productId = Number(id);

    // If invalid ID (e.g., non-numeric), redirect to the add page
    if (isNaN(productId)) {
      redirect(
        `/dashboard/setup/products-services/service/${groupId}/category/${categoryId}/add`,
      );
    }

    try {
      // Prefetch product data
      await client.prefetchQuery({
        queryKey: ['Offering', { id: productId }],
        queryFn: () => OfferingQuery.getById(productId),
      });
    } catch (error) {
      // If fetching fails, also redirect to "add"
      redirect(
        `/dashboard/setup/products-services/service/${groupId}/category/${categoryId}/add`,
      );
    }
  }

  return (
    <HydrationBoundary state={dehydrate(client)}>
      <OfferingGeneralInfo
        id={id === 'add' ? undefined : Number(id)}
        groupId={Number(groupId)}
        categoryId={Number(categoryId)}
      />
    </HydrationBoundary>
  );
}
