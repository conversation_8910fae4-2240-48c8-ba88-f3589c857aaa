import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { getQueryClient } from '@/utils/query-client';
import AppLayout from '@/components/ui/app_layout';
import CompanyDetailView from './components/company-detail-view';

export default async function ExhibitorCompanyViewPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;

    if (Number.isNaN(Number(id))) throw new Error();

    const client = getQueryClient();

    // Prefetch company data
    await client.prefetchQuery({
      queryKey: [...CompanyQuery.tags, { id: Number(id) }],
      queryFn: () => CompanyQuery.getOne(Number(id)),
    });

    // Prefetch company contacts
    await client.prefetchQuery({
      queryKey: [...CompanyQuery.tags, 'contacts', Number(id)],
      queryFn: () => CompanyQuery.contacts.getAll(Number(id)),
    });

    const breadcrumbItems = [
      { title: 'Setup', link: '/dashboard/setup' },
      { title: 'Company-Contact', link: '/dashboard/setup/company-contact' },
      {
        title: 'Exhibitor Companies',
        link: '/dashboard/setup/company-contact/exhibitor-company',
      },
      {
        title: 'Company Details',
        link: `/dashboard/setup/company-contact/exhibitor-company/${id}/view`,
      },
    ];

    return (
      <AppLayout items={breadcrumbItems}>
        <HydrationBoundary state={dehydrate(client)}>
          <CompanyDetailView companyId={Number(id)} />
        </HydrationBoundary>
      </AppLayout>
    );
  } catch (error) {
    redirect('/dashboard/setup/company-contact/exhibitor-company');
  }
}
