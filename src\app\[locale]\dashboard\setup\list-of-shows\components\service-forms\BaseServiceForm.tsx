'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Form } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { ServiceFormType } from '@/models/Service';

interface BaseServiceFormProps<T extends z.ZodType> {
  schema: T;
  defaultValues?: z.infer<T>;
  onSubmit: (data: z.infer<T>) => void;
  onCancel?: () => void;
  isLoading?: boolean;
  serviceType: ServiceFormType;
  children: (form: ReturnType<typeof useForm<z.infer<T>>>) => React.ReactNode;
}

export function BaseServiceForm<T extends z.ZodType>({
  schema,
  defaultValues,
  onSubmit,
  onCancel,
  isLoading = false,
  serviceType,
  children,
}: BaseServiceFormProps<T>) {
  const form = useForm<z.infer<T>>({
    resolver: zodResolver(schema),
    defaultValues,
  });

  const handleSubmit = (data: z.infer<T>) => {
    onSubmit(data);
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 pb-4">
        <h3 className="text-lg font-medium text-gray-900">
          {serviceType
            .replace(/_/g, ' ')
            .toLowerCase()
            .replace(/\b\w/g, (l) => l.toUpperCase())}{' '}
          Service Configuration
        </h3>
        <p className="text-sm text-gray-500 mt-1">
          Please fill out the required information for this service.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {children(form)}

          <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
              >
                Cancel
              </Button>
            )}
            <Button type="submit" variant="main" disabled={isLoading}>
              Save Configuration
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
