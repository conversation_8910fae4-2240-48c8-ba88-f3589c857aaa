'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import { Trash2, Image as ImageIcon } from 'lucide-react';
import Image from 'next/image';
import ShowQuery, { ShowLogo } from '@/services/queries/ShowQuery';
import Field from '@/components/ui/inputs/field';
import { useToast } from '@/components/ui/use-toast';

const logoSchema = z.object({
  file: z
    .any()
    .refine((files) => files?.length > 0, 'Logo file is required')
    .refine(
      (files) => files?.[0]?.type?.startsWith('image/'),
      'Only image files are allowed',
    )
    .refine(
      (files) => files?.[0]?.size <= 5 * 1024 * 1024,
      'File size must be less than 5MB',
    ),
});

type LogoFormValues = z.infer<typeof logoSchema>;

interface ShowLogoTabProps {
  showId?: number;
  onSuccess?: () => void;
}

// Helper function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export default function ShowLogoTab({ showId, onSuccess }: ShowLogoTabProps) {
  const [isUploading, setIsUploading] = useState(false);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const form = useForm<LogoFormValues>({
    resolver: zodResolver(logoSchema),
    defaultValues: {
      file: [],
    },
  });

  // Query to get existing logo
  const { data: logo, isLoading } = useQuery({
    queryKey: ['show-logo', showId],
    queryFn: async (): Promise<ShowLogo | null> => {
      if (!showId) return null;
      try {
        return await ShowQuery.getShowLogo(showId);
      } catch (error: any) {
        // If logo doesn't exist (404), return null
        if (error.status === 404) {
          return null;
        }
        throw error;
      }
    },
    enabled: !!showId,
  });

  // Mutation to upload/update logo
  const uploadMutation = useMutation({
    mutationFn: async (file: File) => {
      if (!showId) throw new Error('Show ID is required');
      return ShowQuery.setShowLogo(showId, file);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['show-logo', showId] });
      queryClient.invalidateQueries({ queryKey: ['shows'] });
      form.reset();
      toast({
        title: 'Success',
        description: logo
          ? 'Logo updated successfully'
          : 'Logo uploaded successfully',
      });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to upload logo',
        variant: 'destructive',
      });
    },
  });

  // Mutation to delete logo
  const deleteMutation = useMutation({
    mutationFn: async () => {
      if (!showId) throw new Error('Show ID is required');
      return ShowQuery.deleteShowLogo(showId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['show-logo', showId] });
      queryClient.invalidateQueries({ queryKey: ['shows'] });
      toast({
        title: 'Success',
        description: 'Logo deleted successfully',
      });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete logo',
        variant: 'destructive',
      });
    },
  });

  const onSubmit = async (data: LogoFormValues) => {
    if (!data.file || data.file.length === 0) return;

    setIsUploading(true);
    try {
      await uploadMutation.mutateAsync(data.file[0]);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this logo?')) {
      deleteMutation.mutate();
    }
  };

  if (!showId) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
            Show Logo
          </h2>
          <div className="min-h-[400px] flex items-center justify-center">
            <div className="text-center">
              <p className="text-slate-500">
                Please save the show first before uploading a logo.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
          Show Logo
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Current Logo Display */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">
                Current Logo
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center h-48">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00646C]"></div>
                </div>
              ) : logo ? (
                <div className="space-y-4">
                  <div className="relative w-full h-48 border border-gray-200 rounded-lg overflow-hidden bg-gray-50">
                    <Image
                      src={`/images${logo.filePath}`}
                      alt="Show Logo"
                      fill
                      className="object-contain"
                      sizes="(max-width: 768px) 100vw, 50vw"
                    />
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p>
                      <strong>File:</strong> {logo.originalFileName}
                    </p>
                    <p>
                      <strong>Size:</strong> {formatFileSize(logo.fileSize)}
                    </p>
                    <p>
                      <strong>Type:</strong> {logo.mimeType}
                    </p>
                    {logo.uploadedAt && (
                      <p>
                        <strong>Uploaded:</strong>{' '}
                        {new Date(logo.uploadedAt).toLocaleDateString()}
                      </p>
                    )}
                    {logo.uploadedByUsername && (
                      <p>
                        <strong>By:</strong> {logo.uploadedByUsername}
                      </p>
                    )}
                  </div>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleDelete}
                    disabled={deleteMutation.isPending}
                    className="w-full"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    {deleteMutation.isPending ? 'Deleting...' : 'Delete Logo'}
                  </Button>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-48 text-gray-500">
                  <ImageIcon className="w-12 h-12 mb-2" />
                  <p>No logo uploaded yet</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Upload Form */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">
                {logo ? 'Update Logo' : 'Upload Logo'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
                >
                  <Field
                    control={form.control}
                    name="file"
                    label="Logo File"
                    required
                    type={{
                      type: 'file',
                      props: {
                        dropzoneOptions: {
                          accept: {
                            'image/*': [
                              '.jpg',
                              '.jpeg',
                              '.png',
                              '.gif',
                              '.webp',
                            ],
                          },
                          multiple: false,
                          maxFiles: 1,
                          maxSize: 5 * 1024 * 1024, // 5MB
                        },
                      },
                    }}
                  />

                  <div className="text-xs text-gray-500 space-y-1">
                    <p>• Supported formats: JPG, PNG, GIF, WebP</p>
                    <p>• Maximum file size: 5MB</p>
                    <p>• Recommended dimensions: 300x300px or larger</p>
                  </div>

                  <Button
                    type="submit"
                    disabled={isUploading || uploadMutation.isPending}
                    className="w-full"
                    iconName={
                      isUploading || uploadMutation.isPending
                        ? 'LoadingIcon'
                        : 'UploadIcon'
                    }
                  >
                    {isUploading || uploadMutation.isPending
                      ? logo
                        ? 'Updating...'
                        : 'Uploading...'
                      : logo
                        ? 'Update Logo'
                        : 'Upload Logo'}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
