import {
  Document,
  DocumentType,
  DocumentStatistics,
  ShowDocumentType,
} from '@/models/Document';
import fetcher from './fetcher';
import { urlToFile } from '@/utils/file-helper';

const DocumentQuery = {
  // Document Types
  getDocumentTypes: async () => fetcher<DocumentType[]>('documents/types'),
  getDocumentType: async (id: number) =>
    fetcher<DocumentType>(`documents/types/${id}`),
  createDocumentType: async (data: { name: string; description: string }) =>
    fetcher('documents/types', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),
  updateDocumentType:
    (id: number) => async (data: { name: string; description: string }) =>
      fetcher(`documents/types/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      }),
  deleteDocumentType: async (id: number) =>
    fetcher(`documents/types/${id}`, { method: 'DELETE' }),

  // Documents
  getDocuments: async () => fetcher<Document[]>('documents'), // This endpoint is not provided, keeping for now.
  getShowDocuments: async (
    showId: number,
    filters?: {
      documentTypeId?: number;
      status?: string;
      isRequired?: boolean;
      isPublic?: boolean;
      isArchived?: boolean;
      uploadedAfter?: string;
      uploadedBefore?: string;
      searchTerm?: string;
    },
  ) => {
    const params = new URLSearchParams();
    if (filters) {
      for (const key in filters) {
        const value = filters[key as keyof typeof filters];
        if (value !== undefined && value !== null) {
          params.append(key, String(value));
        }
      }
    }
    return fetcher<Document[]>(
      `ShowDocuments/show/${showId}?${params.toString()}`,
    );
  },
  getDocument: async (id: number) => {
    const data = await fetcher<Document>(`ShowDocuments/${id}`);
    let file: File[] = [];
    if (data.filePath) {
      const urlFile = await urlToFile(
        '/doc' + data.filePath + '?Protected=true',
      );
      if (urlFile) file = [urlFile];
    }
    return {
      ...data,
      file,
    };
  },
  downloadDocument: async (id: number) =>
    fetcher(`ShowDocuments/${id}/download`, {}, true),
  createDocument: async (formData: FormData) =>
    fetcher(
      'ShowDocuments',
      {
        method: 'POST',
        body: formData,
      },
      true,
    ),
  updateDocument: (id: number) => async (formData: FormData) =>
    fetcher(
      `ShowDocuments/${id}`,
      {
        method: 'PUT',
        body: formData,
      },
      true,
    ),
  deleteDocument: async (id: number) =>
    fetcher(`ShowDocuments/${id}`, { method: 'DELETE' }),
  archiveDocument: async (id: number, archiveReason: string) =>
    fetcher(
      `ShowDocuments/${id}/archive`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ archiveReason }),
      },
      true,
    ),

  // Document Statistics
  getShowDocumentStats: async (showId: number) =>
    fetcher<DocumentStatistics>(`ShowDocuments/show/${showId}/stats`),

  // Available Document Types
  getAvailableDocumentTypes: async () =>
    fetcher<ShowDocumentType[]>(`ShowDocuments/document-types`),
};

export default DocumentQuery;
