import { z } from 'zod';

export const CreateStaffUserSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters'),
  verificationEmail: z.string().email('Please enter a valid email address'),
  mobileNumber: z.string().optional(),
});

export const UpdateStaffUserSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters'),
  verificationEmail: z.string().email('Please enter a valid email address'),
  mobileNumber: z.string().optional(),
});

export type CreateStaffUserFormData = z.infer<typeof CreateStaffUserSchema>;
export type UpdateStaffUserFormData = z.infer<typeof UpdateStaffUserSchema>;
