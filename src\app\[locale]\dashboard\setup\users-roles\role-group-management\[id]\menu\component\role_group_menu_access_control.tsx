'use client';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { type MenuItem } from '@/models/MenuItem';
import Checkbox from '@/components/ui/inputs/checkbox';
import { useMutation } from '@tanstack/react-query';
import { getQueryClient } from '@/utils/query-client';
import { useToast } from '@/components/ui/use-toast';
import RoleGroupQuery from '@/services/queries/RoleGroupQuery';
import AuthQuery from '@/services/queries/AuthQuery';
import { Badge } from '@/components/ui/Badge';

export function RoleGroupMenuAccessControl({
  menuItems = [],
  selectedItems,
  roleGroupId,
  roleGroupName,
  sections,
}: {
  menuItems: MenuItem[];
  selectedItems: number[];
  roleGroupId: number;
  roleGroupName: string;
  sections: string[];
}) {
  const [checkedItems, setCheckedItems] = useState<Set<number>>(
    new Set(selectedItems),
  );
  const { toast } = useToast();
  const { mutate, isPending } = useMutation({
    mutationFn: RoleGroupQuery.setMenu(roleGroupId),
    onSuccess: async (data) => {
      await getQueryClient().invalidateQueries({
        queryKey: [...RoleGroupQuery.tags, { id: roleGroupId }],
      });
      await getQueryClient().invalidateQueries({
        queryKey: [AuthQuery.tags.me],
      });

      toast({
        variant: 'success',
        title: 'Role Group Menu Updated',
        description: `Menu access updated for all roles in "${roleGroupName}" group`,
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to update role group menu access',
      });
    },
  });

  useEffect(() => {
    setCheckedItems(new Set(selectedItems));
  }, [selectedItems]);

  const handlePermissionChange = (itemId: number, checked: boolean) => {
    const newCheckedItems = new Set(checkedItems);
    if (checked) {
      newCheckedItems.add(itemId);
    } else {
      newCheckedItems.delete(itemId);
    }
    setCheckedItems(newCheckedItems);
  };

  const handleParentChange = (parent: MenuItem, checked: boolean) => {
    const newCheckedItems = new Set(checkedItems);

    if (checked) {
      newCheckedItems.add(parent.id);
      parent.children?.forEach((child) => {
        newCheckedItems.add(child.id);
      });
    } else {
      newCheckedItems.delete(parent.id);
      parent.children?.forEach((child) => {
        newCheckedItems.delete(child.id);
      });
    }
    setCheckedItems(newCheckedItems);
  };

  const handleSelectAll = () => {
    const allItemIds = new Set<number>();
    menuItems.forEach((item) => {
      allItemIds.add(item.id);
      item.children?.forEach((child) => {
        allItemIds.add(child.id);
      });
    });
    setCheckedItems(allItemIds);
  };

  const handleDeselectAll = () => {
    setCheckedItems(new Set());
  };

  const isItemChecked = (item: MenuItem): boolean => checkedItems.has(item.id);

  const renderMenuItem = (item: MenuItem) => {
    const isChecked = isItemChecked(item);

    return (
      <div key={item.id} className="py-2 border-b last:border-none">
        <div className="flex items-center space-x-2 group hover:bg-gray-50 rounded-md p-2">
          <Checkbox
            id={item.id.toString()}
            checked={isChecked}
            onCheckedChange={(checked) => {
              if (item.isParent) {
                handleParentChange(item, checked as boolean);
              } else {
                handlePermissionChange(item.id, checked as boolean);
              }
            }}
          />
          <Label
            htmlFor={item.id.toString()}
            className="flex-1 cursor-pointer text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {item.name}
          </Label>
          {item.section && (
            <Badge variant="secondary" className="text-xs">
              {item.section}
            </Badge>
          )}
        </div>
        {item.children && item.children.length > 0 && (
          <div className="ml-6 mt-2 space-y-1">
            {item.children.map((child) => renderMenuItem(child))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div className="w-full space-y-2">
            <CardTitle>Role Group Menu Access Control</CardTitle>
            <CardDescription>
              Select which menu items all roles in the "{roleGroupName}" group
              should have access to. This will update menu access for all roles
              within this group.
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button onClick={handleSelectAll} variant="outline" size="sm">
              Select All
            </Button>
            <Button onClick={handleDeselectAll} variant="outline" size="sm">
              Deselect All
            </Button>
            <Button
              onClick={() => mutate(Array.from(checkedItems))}
              disabled={isPending}
              className="w-fit self-end"
              variant={'main'}
              iconName="SaveIcon"
              iconProps={{ className: 'text-success-foreground', size: 20 }}
            >
              {isPending ? 'Saving...' : 'Save Group Menu Access'}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="pr-4">
          {menuItems.length > 0 ? (
            menuItems
              .sort((a, b) => a.displayOrder - b.displayOrder)
              .map((item) => renderMenuItem(item))
          ) : (
            <div className="text-center py-8 text-gray-500">
              No menu items available
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
