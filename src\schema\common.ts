import { z } from 'zod';

import { LANGUAGES } from '@/common/constants';

export const PasswordSchema = z
  .string()
  .min(4, { message: 'Password must be at least 4 characters long' })
  .min(8, { message: 'Password must be at least 8 characters long' }) // Minimum length of 8 characters
  .max(100) // Maximum length of 100 characters
  .regex(/^(?=.*[a-z])(?=.*[A-Z]).{8,}$/, {
    message:
      'Password must contain at least one lowercase letter and one uppercase letter',
  });
export const NumberString = z
  .string()
  .refine((val) => !Number.isNaN(parseInt(val, 10)), {
    message: 'Expected number, received a string',
  });
export function langSchema(
  schema: any,
  requiredLangs: string[] = ['fr'],
  lang?: string[],
) {
  return z.object(
    Object.fromEntries(
      (lang ?? LANGUAGES.map((c) => c.langCode)).map((i) => [
        i,
        requiredLangs.includes(i)
          ? schema
          : z.union([schema, z.literal('').optional()]).optional(),
      ]),
    ),
  );
}
//This allow object to be called again at render time which may revalidate the state of the generated object
export const zodReviver = (obj: any) => obj;
