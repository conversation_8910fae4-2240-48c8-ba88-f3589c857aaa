import { useQuery } from '@tanstack/react-query';
import DocumentQuery from '@/services/queries/DocumentQuery';
import { DocumentIcon, DownloadIcon } from '@/assets/Icons';
import fetcher from '@/services/queries/fetcher';
import { toast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface DocumentsProps {
  showId: number;
}

export default function Documents({ showId }: DocumentsProps) {
  const { data: documents, isLoading: loadingDocuments } = useQuery({
    queryKey: ['documents', { showId }],
    queryFn: () =>
      DocumentQuery.getShowDocuments(showId, { isArchived: false }),
    enabled: !!showId,
  });

  const handleDownload = async (id: number, name: string) => {
    try {
      const blob = (await fetcher(
        `ShowDocuments/${id}/download`,
        {},
        true,
      )) as Blob;
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = name;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (e: any) {
      toast({
        title: e.message || 'Failed to download document',
        variant: 'destructive',
      });
    }
  };

  if (loadingDocuments) {
    return (
      <Card>
        <CardHeader className="pb-5">
          <CardTitle>Show Documents Available:</CardTitle>
        </CardHeader>
        <CardContent className="min-h-[100px] flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading documents...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!documents || documents.length === 0) {
    return (
      <Card>
        <CardHeader className="pb-5">
          <CardTitle>Show Documents Available:</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            No documents available for this show.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-5">
        <CardTitle>Show Documents Available:</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {documents.map((document) => (
            <div
              key={document.id}
              onClick={() =>
                handleDownload(document.id, document.originalFileName)
              }
              className="flex items-center justify-between px-3 py-2 border border-slate-200 rounded-md shadow-sm bg-white cursor-pointer hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center gap-2">
                <DocumentIcon className="h-5 w-5 text-main" />
                <span className="font-medium text-sm text-gray-700">
                  {document.name}
                </span>
              </div>
              <DownloadIcon className="h-5 w-5 text-gray-500" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
